'use client'

import { useState, useRef } from 'react'
import { 
  DocumentArrowUpIcon, 
  XMarkIcon, 
  ArrowUpTrayIcon,
  DocumentIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { toast } from 'react-hot-toast'

interface DownloadableFile {
  fileName: string
  fileUrl: string
  fileSize: number
  fileType: string
}

interface DownloadableFileUploadProps {
  file: DownloadableFile | null
  onFileChange: (file: DownloadableFile | null) => void
  productId?: string
  disabled?: boolean
}

export function DownloadableFileUpload({ 
  file, 
  onFileChange, 
  productId,
  disabled = false 
}: DownloadableFileUploadProps) {
  const [uploading, setUploading] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (!selectedFile) return

    // Validate file size (max 100MB)
    const maxSize = 100 * 1024 * 1024
    if (selectedFile.size > maxSize) {
      toast.error('File too large (max 100MB)')
      return
    }

    setUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('type', 'product-file')
      if (productId) {
        formData.append('productId', productId)
      }

      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(error.message || 'Upload failed')
      }

      const data = await response.json()
      
      const uploadedFile: DownloadableFile = {
        fileName: data.data.originalName || selectedFile.name,
        fileUrl: data.data.url,
        fileSize: selectedFile.size,
        fileType: selectedFile.type
      }

      onFileChange(uploadedFile)
      toast.success('File uploaded successfully')
      
      // Clear the input
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error: any) {
      console.error('Error uploading file:', error)
      toast.error(error.message || 'Failed to upload file')
    } finally {
      setUploading(false)
    }
  }

  const removeFile = () => {
    onFileChange(null)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-white">Downloadable File</h3>
        {file && (
          <Button
            type="button"
            variant="outline"
            size="sm"
            onClick={removeFile}
            disabled={disabled}
            className="text-red-400 border-red-400 hover:bg-red-400 hover:text-white"
          >
            <XMarkIcon className="h-4 w-4 mr-1" />
            Remove
          </Button>
        )}
      </div>

      {file ? (
        // Show uploaded file
        <div className="bg-gray-800 border border-gray-600 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-600 rounded-lg flex items-center justify-center flex-shrink-0">
              <CheckCircleIcon className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-white font-medium truncate">{file.fileName}</p>
              <p className="text-gray-400 text-sm">
                {formatFileSize(file.fileSize)} • {file.fileType || 'Unknown type'}
              </p>
            </div>
            <DocumentIcon className="w-6 h-6 text-gray-400" />
          </div>
        </div>
      ) : (
        // Show upload area
        <div className="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center hover:border-gray-500 transition-colors">
          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileSelect}
            disabled={disabled || uploading}
            className="hidden"
            accept=".zip,.rar,.7z,.ex4,.ex5,.mq4,.mq5,.dll,.pdf,.txt,.doc,.docx"
          />
          
          <div className="text-center">
            <DocumentArrowUpIcon className="mx-auto h-12 w-12 text-gray-400" />
            <div className="mt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => fileInputRef.current?.click()}
                disabled={disabled || uploading}
                className="border-gray-600 text-gray-300 hover:bg-gray-700"
              >
                {uploading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-300 mr-2"></div>
                    Uploading...
                  </>
                ) : (
                  <>
                    <ArrowUpTrayIcon className="h-4 w-4 mr-2" />
                    Choose File
                  </>
                )}
              </Button>
            </div>
            <p className="mt-2 text-sm text-gray-400">
              ZIP, RAR, EX4, EX5, MQ4, MQ5, PDF, DOC files up to 100MB
            </p>
            <p className="text-xs text-gray-500 mt-1">
              This file will be available for download after purchase
            </p>
          </div>
        </div>
      )}
    </div>
  )
}
