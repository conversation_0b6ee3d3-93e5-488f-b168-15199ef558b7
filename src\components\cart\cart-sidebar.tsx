'use client'

import { Fragment } from 'react'
import { Dialog, Transition } from '@headlessui/react'
import { XMarkIcon, ShoppingBagIcon, TrashIcon } from '@heroicons/react/24/outline'
import { useCartStore } from '@/store/cart-store'
import { Button } from '@/components/ui/button'
import { formatCurrency } from '@/lib/utils'
import Image from 'next/image'
import Link from 'next/link'

export function CartSidebar() {
  const { 
    items, 
    isOpen, 
    closeCart, 
    removeItem, 
    updateQuantity, 
    getTotalPrice,
    getTotalItems 
  } = useCartStore()

  const totalItems = getTotalItems()
  const totalPrice = getTotalPrice()

  return (
    <Transition.Root show={isOpen} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={closeCart}>
        <Transition.Child
          as={Fragment}
          enter="ease-in-out duration-500"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in-out duration-500"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-75 transition-opacity" />
        </Transition.Child>

        <div className="fixed inset-0 overflow-hidden">
          <div className="absolute inset-0 overflow-hidden">
            <div className="pointer-events-none fixed inset-y-0 right-0 flex max-w-full pl-10">
              <Transition.Child
                as={Fragment}
                enter="transform transition ease-in-out duration-500 sm:duration-700"
                enterFrom="translate-x-full"
                enterTo="translate-x-0"
                leave="transform transition ease-in-out duration-500 sm:duration-700"
                leaveFrom="translate-x-0"
                leaveTo="translate-x-full"
              >
                <Dialog.Panel className="pointer-events-auto w-screen max-w-md">
                  <div className="flex h-full flex-col bg-gray-900 shadow-xl">
                    {/* Header */}
                    <div className="flex items-center justify-between px-4 py-6 border-b border-gray-700">
                      <Dialog.Title className="text-lg font-semibold text-white flex items-center">
                        <ShoppingBagIcon className="w-6 h-6 mr-2" />
                        Shopping Cart ({totalItems})
                      </Dialog.Title>
                      <button
                        type="button"
                        className="text-gray-400 hover:text-white transition-colors duration-200"
                        onClick={closeCart}
                      >
                        <XMarkIcon className="w-6 h-6" />
                      </button>
                    </div>

                    {/* Cart Items */}
                    <div className="flex-1 overflow-y-auto px-4 py-6">
                      {items.length === 0 ? (
                        <div className="text-center py-12">
                          <ShoppingBagIcon className="w-16 h-16 text-gray-600 mx-auto mb-4" />
                          <p className="text-gray-400 text-lg mb-4">Your cart is empty</p>
                          <Button 
                            onClick={closeCart}
                            variant="premium"
                            className="w-full"
                          >
                            Continue Shopping
                          </Button>
                        </div>
                      ) : (
                        <div className="space-y-6">
                          {items.map((item) => {
                            const effectivePrice = item.product.isOnSale && item.product.salePrice
                              ? item.product.salePrice
                              : item.product.price

                            return (
                              <div key={item.id} className="flex items-center space-x-4 bg-gray-800/50 rounded-lg p-4">
                                {/* Product Image */}
                                <div className="flex-shrink-0 w-16 h-16 bg-gray-700 rounded-lg overflow-hidden">
                                  {item.product.images && item.product.images.length > 0 ? (
                                    <Image
                                      src={item.product.images[0]}
                                      alt={item.product.name}
                                      width={64}
                                      height={64}
                                      className="w-full h-full object-cover"
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center">
                                      <ShoppingBagIcon className="w-6 h-6 text-gray-500" />
                                    </div>
                                  )}
                                </div>

                                {/* Product Details */}
                                <div className="flex-1 min-w-0">
                                  <h3 className="text-white font-medium text-sm line-clamp-2">
                                    {item.product.name}
                                  </h3>
                                  <p className="text-gray-400 text-xs mt-1">
                                    {item.product.category.name}
                                  </p>
                                  <div className="flex items-center justify-between mt-2">
                                    <div className="flex items-center space-x-2">
                                      <span className="text-yellow-400 font-semibold">
                                        {formatCurrency(Number(effectivePrice))}
                                      </span>
                                      {item.product.isOnSale && item.product.originalPrice && (
                                        <span className="text-gray-500 text-xs line-through">
                                          {formatCurrency(Number(item.product.originalPrice))}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                </div>

                                {/* Quantity Controls */}
                                <div className="flex flex-col items-end space-y-2">
                                  <div className="flex items-center space-x-2">
                                    <button
                                      onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                                      className="w-6 h-6 rounded-full bg-gray-700 text-white text-sm hover:bg-gray-600 transition-colors duration-200"
                                    >
                                      -
                                    </button>
                                    <span className="text-white text-sm w-8 text-center">
                                      {item.quantity}
                                    </span>
                                    <button
                                      onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                                      className="w-6 h-6 rounded-full bg-gray-700 text-white text-sm hover:bg-gray-600 transition-colors duration-200"
                                    >
                                      +
                                    </button>
                                  </div>
                                  <button
                                    onClick={() => removeItem(item.product.id)}
                                    className="text-red-400 hover:text-red-300 transition-colors duration-200"
                                  >
                                    <TrashIcon className="w-4 h-4" />
                                  </button>
                                </div>
                              </div>
                            )
                          })}
                        </div>
                      )}
                    </div>

                    {/* Footer */}
                    {items.length > 0 && (
                      <div className="border-t border-gray-700 px-4 py-6">
                        <div className="flex items-center justify-between mb-4">
                          <span className="text-lg font-semibold text-white">Total</span>
                          <span className="text-2xl font-bold text-yellow-400">
                            {formatCurrency(totalPrice)}
                          </span>
                        </div>
                        
                        <div className="space-y-3">
                          <Link href="/checkout" onClick={closeCart}>
                            <Button variant="premium" className="w-full">
                              Proceed to Checkout
                            </Button>
                          </Link>
                          <Button 
                            variant="outline" 
                            className="w-full border-gray-600 text-gray-300 hover:bg-gray-800"
                            onClick={closeCart}
                          >
                            Continue Shopping
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </div>
      </Dialog>
    </Transition.Root>
  )
}
