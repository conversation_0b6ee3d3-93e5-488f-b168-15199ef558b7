import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAuth } from '@/lib/auth-utils'
import { z } from 'zod'

const addToCartSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  quantity: z.number().min(1, 'Quantity must be at least 1').default(1),
})

const updateCartSchema = z.object({
  productId: z.string().min(1, 'Product ID is required'),
  quantity: z.number().min(0, 'Quantity must be non-negative'),
})

// GET /api/cart - Get user's cart
export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()

    const cartItems = await prisma.cartItem.findMany({
      where: { userId: user.id },
      include: {
        product: {
          include: {
            category: true,
            _count: {
              select: {
                reviews: true,
                downloads: true
              }
            }
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    })

    // Calculate totals
    const subtotal = cartItems.reduce((total, item) => {
      const price = item.product.isOnSale && item.product.salePrice
        ? item.product.salePrice
        : item.product.price
      return total + (Number(price) * item.quantity)
    }, 0)

    const totalItems = cartItems.reduce((total, item) => total + item.quantity, 0)

    return NextResponse.json({
      success: true,
      data: {
        items: cartItems,
        subtotal,
        total: subtotal, // No tax or shipping for digital products
        itemCount: totalItems
      }
    })

  } catch (error: any) {
    console.error('Error fetching cart:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch cart'
    }, { status: 500 })
  }
}

// POST /api/cart - Add item to cart
export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()
    const body = await request.json()
    const { productId, quantity } = addToCartSchema.parse(body)

    // Verify product exists and is published
    const product = await prisma.product.findUnique({
      where: { 
        id: productId,
        status: 'PUBLISHED'
      }
    })

    if (!product) {
      return NextResponse.json({
        success: false,
        message: 'Product not found or not available'
      }, { status: 404 })
    }

    // Check if item already exists in cart
    const existingItem = await prisma.cartItem.findUnique({
      where: {
        userId_productId: {
          userId: user.id,
          productId
        }
      }
    })

    let cartItem
    if (existingItem) {
      // Update quantity
      cartItem = await prisma.cartItem.update({
        where: { id: existingItem.id },
        data: { quantity: existingItem.quantity + quantity },
        include: {
          product: {
            include: {
              category: true
            }
          }
        }
      })
    } else {
      // Create new cart item
      cartItem = await prisma.cartItem.create({
        data: {
          userId: user.id,
          productId,
          quantity
        },
        include: {
          product: {
            include: {
              category: true
            }
          }
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'Item added to cart',
      data: cartItem
    })

  } catch (error: any) {
    console.error('Error adding to cart:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to add item to cart'
    }, { status: 500 })
  }
}

// PUT /api/cart - Update cart item quantity
export async function PUT(request: NextRequest) {
  try {
    const user = await requireAuth()
    const body = await request.json()
    const { productId, quantity } = updateCartSchema.parse(body)

    if (quantity === 0) {
      // Remove item from cart
      await prisma.cartItem.delete({
        where: {
          userId_productId: {
            userId: user.id,
            productId
          }
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Item removed from cart'
      })
    }

    // Update quantity
    const cartItem = await prisma.cartItem.update({
      where: {
        userId_productId: {
          userId: user.id,
          productId
        }
      },
      data: { quantity },
      include: {
        product: {
          include: {
            category: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Cart updated',
      data: cartItem
    })

  } catch (error: any) {
    console.error('Error updating cart:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to update cart'
    }, { status: 500 })
  }
}

// DELETE /api/cart - Clear cart or remove specific item
export async function DELETE(request: NextRequest) {
  try {
    const user = await requireAuth()
    const { searchParams } = new URL(request.url)
    const productId = searchParams.get('productId')

    if (productId) {
      // Remove specific item
      await prisma.cartItem.delete({
        where: {
          userId_productId: {
            userId: user.id,
            productId
          }
        }
      })

      return NextResponse.json({
        success: true,
        message: 'Item removed from cart'
      })
    } else {
      // Clear entire cart
      await prisma.cartItem.deleteMany({
        where: { userId: user.id }
      })

      return NextResponse.json({
        success: true,
        message: 'Cart cleared'
      })
    }

  } catch (error: any) {
    console.error('Error removing from cart:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to remove from cart'
    }, { status: 500 })
  }
}
