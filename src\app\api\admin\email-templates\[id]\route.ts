import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'
import { z } from 'zod'
import { EmailTemplateType } from '@prisma/client'

const updateEmailTemplateSchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  subject: z.string().min(1, 'Subject is required').optional(),
  htmlContent: z.string().min(1, 'HTML content is required').optional(),
  textContent: z.string().optional(),
  type: z.nativeEnum(EmailTemplateType).optional(),
  variables: z.record(z.string()).optional(),
  isActive: z.boolean().optional(),
})

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()

    const template = await prisma.emailTemplate.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { emailLogs: true }
        }
      }
    })

    if (!template) {
      return NextResponse.json({
        success: false,
        message: 'Email template not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: template
    })

  } catch (error: any) {
    console.error('Error fetching email template:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch email template'
    }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()

    const body = await request.json()
    const validatedData = updateEmailTemplateSchema.parse(body)

    const template = await prisma.emailTemplate.update({
      where: { id: params.id },
      data: validatedData
    })

    return NextResponse.json({
      success: true,
      message: 'Email template updated successfully',
      data: template
    })

  } catch (error: any) {
    console.error('Error updating email template:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    if (error.code === 'P2025') {
      return NextResponse.json({
        success: false,
        message: 'Email template not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to update email template'
    }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()

    await prisma.emailTemplate.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: 'Email template deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting email template:', error)
    
    if (error.code === 'P2025') {
      return NextResponse.json({
        success: false,
        message: 'Email template not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to delete email template'
    }, { status: 500 })
  }
}
