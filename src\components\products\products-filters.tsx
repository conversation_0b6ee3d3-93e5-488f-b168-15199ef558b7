'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  FunnelIcon,
  XMarkIcon,
  TagIcon,
  CurrencyDollarIcon 
} from '@heroicons/react/24/outline'

interface Category {
  id: string
  name: string
  slug: string
}

export function ProductsFilters() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [categories, setCategories] = useState<Category[]>([])
  const [isOpen, setIsOpen] = useState(false)

  // Get current filter values from URL
  const currentCategory = searchParams.get('category') || ''
  const currentMinPrice = searchParams.get('minPrice') || ''
  const currentMaxPrice = searchParams.get('maxPrice') || ''
  const currentFeatured = searchParams.get('featured') === 'true'
  const currentOnSale = searchParams.get('onSale') === 'true'

  useEffect(() => {
    // Fetch categories
    const fetchCategories = async () => {
      try {
        const response = await fetch('/api/categories')
        const result = await response.json()
        if (result.success && Array.isArray(result.data)) {
          setCategories(result.data)
        } else {
          console.error('Invalid categories response:', result)
          setCategories([])
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
        setCategories([])
      }
    }

    fetchCategories()
  }, [])

  const updateFilters = (key: string, value: string | boolean) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (value === '' || value === false) {
      params.delete(key)
    } else {
      params.set(key, String(value))
    }
    
    // Reset to first page when filters change
    params.delete('page')
    
    router.push(`/products?${params.toString()}`)
  }

  const clearAllFilters = () => {
    router.push('/products')
  }

  const hasActiveFilters = currentCategory || currentMinPrice || currentMaxPrice || currentFeatured || currentOnSale

  return (
    <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <FunnelIcon className="w-5 h-5 text-yellow-400 mr-2" />
          <h3 className="text-lg font-semibold text-white">Filters</h3>
        </div>
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="text-gray-400 hover:text-white"
          >
            <XMarkIcon className="w-4 h-4 mr-1" />
            Clear
          </Button>
        )}
      </div>

      <div className="space-y-6">
        {/* Categories */}
        <div>
          <Label className="text-white font-medium mb-3 block">Category</Label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="radio"
                name="category"
                value=""
                checked={currentCategory === ''}
                onChange={(e) => updateFilters('category', e.target.value)}
                className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-600 bg-gray-800"
              />
              <span className="ml-2 text-gray-300">All Categories</span>
            </label>
            {categories.map((category) => (
              <label key={category.id} className="flex items-center">
                <input
                  type="radio"
                  name="category"
                  value={category.slug}
                  checked={currentCategory === category.slug}
                  onChange={(e) => updateFilters('category', e.target.value)}
                  className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-600 bg-gray-800"
                />
                <span className="ml-2 text-gray-300">{category.name}</span>
              </label>
            ))}
          </div>
        </div>

        {/* Price Range */}
        <div>
          <Label className="text-white font-medium mb-3 block">
            <CurrencyDollarIcon className="w-4 h-4 inline mr-1" />
            Price Range
          </Label>
          <div className="grid grid-cols-2 gap-2">
            <div>
              <Input
                type="number"
                placeholder="Min"
                value={currentMinPrice}
                onChange={(e) => updateFilters('minPrice', e.target.value)}
                className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-500 focus:ring-yellow-500"
              />
            </div>
            <div>
              <Input
                type="number"
                placeholder="Max"
                value={currentMaxPrice}
                onChange={(e) => updateFilters('maxPrice', e.target.value)}
                className="bg-gray-800 border-gray-600 text-white placeholder-gray-400 focus:border-yellow-500 focus:ring-yellow-500"
              />
            </div>
          </div>
        </div>

        {/* Special Filters */}
        <div>
          <Label className="text-white font-medium mb-3 block">
            <TagIcon className="w-4 h-4 inline mr-1" />
            Special Offers
          </Label>
          <div className="space-y-2">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={currentFeatured}
                onChange={(e) => updateFilters('featured', e.target.checked)}
                className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-600 rounded bg-gray-800"
              />
              <span className="ml-2 text-gray-300">Featured Products</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={currentOnSale}
                onChange={(e) => updateFilters('onSale', e.target.checked)}
                className="h-4 w-4 text-yellow-500 focus:ring-yellow-500 border-gray-600 rounded bg-gray-800"
              />
              <span className="ml-2 text-gray-300">On Sale</span>
            </label>
          </div>
        </div>

        {/* Popular Tags */}
        <div>
          <Label className="text-white font-medium mb-3 block">Popular Tags</Label>
          <div className="flex flex-wrap gap-2">
            {['MT4', 'MT5', 'Scalping', 'Grid', 'Martingale', 'News'].map((tag) => (
              <button
                key={tag}
                onClick={() => {
                  const params = new URLSearchParams(searchParams.toString())
                  const currentTags = params.get('tags')?.split(',') || []
                  
                  if (currentTags.includes(tag)) {
                    const newTags = currentTags.filter(t => t !== tag)
                    if (newTags.length > 0) {
                      params.set('tags', newTags.join(','))
                    } else {
                      params.delete('tags')
                    }
                  } else {
                    const newTags = [...currentTags, tag]
                    params.set('tags', newTags.join(','))
                  }
                  
                  params.delete('page')
                  router.push(`/products?${params.toString()}`)
                }}
                className={`px-3 py-1 rounded-full text-xs font-medium transition-colors duration-200 ${
                  searchParams.get('tags')?.split(',').includes(tag)
                    ? 'bg-yellow-500 text-black'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {tag}
              </button>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
