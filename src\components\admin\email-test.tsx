'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { EnvelopeIcon } from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

export function EmailTest() {
  const [testEmail, setTestEmail] = useState('')
  const [testSubject, setTestSubject] = useState('Test Email from Forex Bot Zone')
  const [testMessage, setTestMessage] = useState('This is a test email to verify the email system is working correctly.')
  const [welcomeEmail, setWelcomeEmail] = useState('')
  const [welcomeName, setWelcomeName] = useState('')
  const [loading, setLoading] = useState<string | null>(null)

  const sendTestEmail = async () => {
    if (!testEmail || !testSubject || !testMessage) {
      toast.error('Please fill in all fields')
      return
    }

    setLoading('test')
    try {
      const response = await fetch('/api/email/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          to: testEmail,
          subject: testSubject,
          message: testMessage
        })
      })

      const data = await response.json()
      if (data.success) {
        toast.success('Test email sent successfully!')
      } else {
        toast.error(data.message || 'Failed to send test email')
      }
    } catch (error) {
      console.error('Test email error:', error)
      toast.error('Failed to send test email')
    } finally {
      setLoading(null)
    }
  }

  const sendWelcomeEmail = async () => {
    if (!welcomeEmail || !welcomeName) {
      toast.error('Please fill in email and name')
      return
    }

    setLoading('welcome')
    try {
      const response = await fetch('/api/email/welcome', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: welcomeEmail,
          firstName: welcomeName
        })
      })

      const data = await response.json()
      if (data.success) {
        toast.success('Welcome email sent successfully!')
      } else {
        toast.error(data.message || 'Failed to send welcome email')
      }
    } catch (error) {
      console.error('Welcome email error:', error)
      toast.error('Failed to send welcome email')
    } finally {
      setLoading(null)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-2">
        <EnvelopeIcon className="h-6 w-6 text-yellow-400" />
        <h2 className="text-2xl font-bold text-white">Email System Test</h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Test Email */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Send Test Email</CardTitle>
            <CardDescription className="text-gray-400">
              Send a test email to verify the email system is working
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="testEmail" className="text-white">Email Address</Label>
              <Input
                id="testEmail"
                type="email"
                value={testEmail}
                onChange={(e) => setTestEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            
            <div>
              <Label htmlFor="testSubject" className="text-white">Subject</Label>
              <Input
                id="testSubject"
                value={testSubject}
                onChange={(e) => setTestSubject(e.target.value)}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            
            <div>
              <Label htmlFor="testMessage" className="text-white">Message</Label>
              <Textarea
                id="testMessage"
                value={testMessage}
                onChange={(e) => setTestMessage(e.target.value)}
                rows={4}
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            
            <Button
              onClick={sendTestEmail}
              disabled={loading === 'test'}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {loading === 'test' ? 'Sending...' : 'Send Test Email'}
            </Button>
          </CardContent>
        </Card>

        {/* Welcome Email */}
        <Card className="bg-gray-800 border-gray-700">
          <CardHeader>
            <CardTitle className="text-white">Send Welcome Email</CardTitle>
            <CardDescription className="text-gray-400">
              Send a welcome email to a new user
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="welcomeEmail" className="text-white">Email Address</Label>
              <Input
                id="welcomeEmail"
                type="email"
                value={welcomeEmail}
                onChange={(e) => setWelcomeEmail(e.target.value)}
                placeholder="<EMAIL>"
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            
            <div>
              <Label htmlFor="welcomeName" className="text-white">First Name</Label>
              <Input
                id="welcomeName"
                value={welcomeName}
                onChange={(e) => setWelcomeName(e.target.value)}
                placeholder="John"
                className="bg-gray-700 border-gray-600 text-white"
              />
            </div>
            
            <Button
              onClick={sendWelcomeEmail}
              disabled={loading === 'welcome'}
              className="w-full bg-green-600 hover:bg-green-700"
            >
              {loading === 'welcome' ? 'Sending...' : 'Send Welcome Email'}
            </Button>
          </CardContent>
        </Card>
      </div>

      <Card className="bg-gray-800 border-gray-700">
        <CardHeader>
          <CardTitle className="text-white">Email Configuration Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">✓</div>
              <p className="text-white font-medium">Resend API</p>
              <p className="text-gray-400 text-sm">Configured</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">✓</div>
              <p className="text-white font-medium">Email Templates</p>
              <p className="text-gray-400 text-sm">Ready</p>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-400">✓</div>
              <p className="text-white font-medium">API Endpoints</p>
              <p className="text-gray-400 text-sm">Active</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
