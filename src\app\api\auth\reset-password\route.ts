import { NextRequest, NextResponse } from 'next/server'
import { resetPassword } from '@/lib/auth-utils'
import { z } from 'zod'

const resetPasswordSchema = z.object({
  token: z.string().min(1, 'Token is required'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const { token, password } = resetPasswordSchema.parse(body)
    
    // Reset password
    const user = await resetPassword(token, password)
    
    return NextResponse.json({
      success: true,
      message: 'Password reset successfully',
      data: user
    })
    
  } catch (error: any) {
    console.error('Reset password error:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Password reset failed'
    }, { status: 400 })
  }
}
