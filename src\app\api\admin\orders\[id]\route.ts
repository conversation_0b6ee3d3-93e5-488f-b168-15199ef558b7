import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'
import { z } from 'zod'
import { OrderStatus } from '@prisma/client'

const updateOrderSchema = z.object({
  status: z.nativeEnum(OrderStatus).optional(),
})

export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()

    const body = await request.json()
    const validatedData = updateOrderSchema.parse(body)

    const order = await prisma.order.update({
      where: { id: params.id },
      data: validatedData,
      include: {
        user: {
          select: {
            id: true,
            email: true,
            firstName: true,
            lastName: true,
          }
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
                slug: true,
                price: true,
              }
            }
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Order updated successfully',
      data: order
    })

  } catch (error: any) {
    console.error('Error updating order:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to update order'
    }, { status: 500 })
  }
}
