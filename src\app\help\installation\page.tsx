import Link from 'next/link'
import { 
  ArrowLeftIcon,
  DocumentArrowDownIcon,
  FolderIcon,
  PlayIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

export default function InstallationGuidePage() {
  const installationSteps = {
    mt4: [
      {
        step: 1,
        title: 'Download the Expert Advisor',
        description: 'After purchase, download the .ex4 or .mq4 file from your account downloads.',
        icon: DocumentArrowDownIcon
      },
      {
        step: 2,
        title: 'Open MetaTrader 4',
        description: 'Launch your MT4 platform and ensure you are connected to your broker.',
        icon: PlayIcon
      },
      {
        step: 3,
        title: 'Access the Data Folder',
        description: 'Go to File → Open Data Folder in MT4 menu.',
        icon: FolderIcon
      },
      {
        step: 4,
        title: 'Navigate to Experts Folder',
        description: 'Open MQL4 → Experts folder in the data directory.',
        icon: FolderIcon
      },
      {
        step: 5,
        title: 'Copy the EA File',
        description: 'Copy your downloaded .ex4 or .mq4 file into the Experts folder.',
        icon: DocumentArrowDownIcon
      },
      {
        step: 6,
        title: 'Restart MT4',
        description: 'Close and restart MetaTrader 4 to load the new Expert Advisor.',
        icon: PlayIcon
      },
      {
        step: 7,
        title: 'Attach to Chart',
        description: 'Drag the EA from Navigator panel onto your desired chart.',
        icon: CheckCircleIcon
      }
    ],
    mt5: [
      {
        step: 1,
        title: 'Download the Expert Advisor',
        description: 'Download the .ex5 or .mq5 file from your account downloads.',
        icon: DocumentArrowDownIcon
      },
      {
        step: 2,
        title: 'Open MetaTrader 5',
        description: 'Launch your MT5 platform and connect to your trading account.',
        icon: PlayIcon
      },
      {
        step: 3,
        title: 'Access the Data Folder',
        description: 'Go to File → Open Data Folder in MT5 menu.',
        icon: FolderIcon
      },
      {
        step: 4,
        title: 'Navigate to Experts Folder',
        description: 'Open MQL5 → Experts folder in the data directory.',
        icon: FolderIcon
      },
      {
        step: 5,
        title: 'Copy the EA File',
        description: 'Copy your downloaded .ex5 or .mq5 file into the Experts folder.',
        icon: DocumentArrowDownIcon
      },
      {
        step: 6,
        title: 'Restart MT5',
        description: 'Close and restart MetaTrader 5 to refresh the Expert Advisors list.',
        icon: PlayIcon
      },
      {
        step: 7,
        title: 'Attach to Chart',
        description: 'Drag the EA from Navigator panel onto your chart and configure settings.',
        icon: CheckCircleIcon
      }
    ]
  }

  const indicators = [
    {
      step: 1,
      title: 'Download the Indicator',
      description: 'Download the .ex4/.ex5 or .mq4/.mq5 indicator file.',
      icon: DocumentArrowDownIcon
    },
    {
      step: 2,
      title: 'Open Data Folder',
      description: 'File → Open Data Folder in your MetaTrader platform.',
      icon: FolderIcon
    },
    {
      step: 3,
      title: 'Navigate to Indicators',
      description: 'Go to MQL4/MQL5 → Indicators folder.',
      icon: FolderIcon
    },
    {
      step: 4,
      title: 'Copy Indicator File',
      description: 'Copy the indicator file into the Indicators folder.',
      icon: DocumentArrowDownIcon
    },
    {
      step: 5,
      title: 'Restart Platform',
      description: 'Restart MetaTrader to load the new indicator.',
      icon: PlayIcon
    },
    {
      step: 6,
      title: 'Apply to Chart',
      description: 'Find the indicator in Navigator and drag it to your chart.',
      icon: CheckCircleIcon
    }
  ]

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            href="/help" 
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors duration-200 mb-4"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Help Center
          </Link>
          <h1 className="text-4xl font-bold text-white mb-4">Installation Guide</h1>
          <p className="text-xl text-gray-300">
            Complete step-by-step instructions for installing Expert Advisors and Indicators
          </p>
        </div>

        {/* Important Notice */}
        <div className="bg-yellow-400/10 border border-yellow-400/20 rounded-xl p-6 mb-8">
          <div className="flex items-start">
            <ExclamationTriangleIcon className="w-6 h-6 text-yellow-400 mr-3 mt-1 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-yellow-400 mb-2">Important Notes</h3>
              <ul className="text-gray-300 space-y-1 text-sm">
                <li>• Always download files from your account downloads page</li>
                <li>• Ensure your MetaTrader platform is closed before copying files</li>
                <li>• Enable "Allow DLL imports" and "Allow live trading" in EA settings</li>
                <li>• Test EAs on demo accounts before using with real money</li>
              </ul>
            </div>
          </div>
        </div>

        {/* MT4 Installation */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">MetaTrader 4 Expert Advisors</h2>
          <div className="space-y-4">
            {installationSteps.mt4.map((step, index) => (
              <div key={index} className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-yellow-400 text-black rounded-full flex items-center justify-center font-bold mr-4">
                    {step.step}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <step.icon className="w-5 h-5 text-yellow-400 mr-2" />
                      <h3 className="text-lg font-semibold text-white">{step.title}</h3>
                    </div>
                    <p className="text-gray-300">{step.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* MT5 Installation */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">MetaTrader 5 Expert Advisors</h2>
          <div className="space-y-4">
            {installationSteps.mt5.map((step, index) => (
              <div key={index} className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-yellow-400 text-black rounded-full flex items-center justify-center font-bold mr-4">
                    {step.step}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <step.icon className="w-5 h-5 text-yellow-400 mr-2" />
                      <h3 className="text-lg font-semibold text-white">{step.title}</h3>
                    </div>
                    <p className="text-gray-300">{step.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Indicators Installation */}
        <div className="mb-12">
          <h2 className="text-2xl font-bold text-white mb-6">Custom Indicators</h2>
          <div className="space-y-4">
            {indicators.map((step, index) => (
              <div key={index} className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
                <div className="flex items-start">
                  <div className="flex-shrink-0 w-10 h-10 bg-yellow-400 text-black rounded-full flex items-center justify-center font-bold mr-4">
                    {step.step}
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center mb-2">
                      <step.icon className="w-5 h-5 text-yellow-400 mr-2" />
                      <h3 className="text-lg font-semibold text-white">{step.title}</h3>
                    </div>
                    <p className="text-gray-300">{step.description}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Troubleshooting */}
        <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6 mb-8">
          <h3 className="text-xl font-semibold text-white mb-4">Troubleshooting</h3>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-yellow-400 mb-2">EA not appearing in Navigator?</h4>
              <p className="text-gray-300 text-sm">Make sure you copied the file to the correct folder and restarted MetaTrader completely.</p>
            </div>
            <div>
              <h4 className="font-semibold text-yellow-400 mb-2">EA not trading automatically?</h4>
              <p className="text-gray-300 text-sm">Check that "Allow live trading" is enabled in Tools → Options → Expert Advisors.</p>
            </div>
            <div>
              <h4 className="font-semibold text-yellow-400 mb-2">Getting DLL import errors?</h4>
              <p className="text-gray-300 text-sm">Enable "Allow DLL imports" in the EA settings when attaching to chart.</p>
            </div>
          </div>
        </div>

        {/* Need Help */}
        <div className="text-center">
          <h3 className="text-xl font-semibold text-white mb-4">Need Additional Help?</h3>
          <p className="text-gray-300 mb-6">
            If you're still having trouble with installation, our support team is ready to assist you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="btn-secondary">
              Contact Support
            </Link>
            <Link href="/help/chat" className="btn-primary">
              Live Chat Support
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
