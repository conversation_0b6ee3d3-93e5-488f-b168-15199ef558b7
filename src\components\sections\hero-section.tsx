'use client'

import { useEffect, useState } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import {
  ChartBarIcon,
  CurrencyDollarIcon,
  TrophyIcon,
  ArrowRightIcon,
  PlayIcon
} from '@heroicons/react/24/outline'

const stats = [
  { label: 'Active Traders', value: '10,000+', icon: ChartBarIcon },
  { label: 'Total Downloads', value: '50,000+', icon: CurrencyDollarIcon },
  { label: 'Success Rate', value: '95%', icon: TrophyIcon },
]

const floatingElements = [
  { id: 1, x: 10, y: 20, size: 'w-2 h-2', delay: 0 },
  { id: 2, x: 80, y: 10, size: 'w-3 h-3', delay: 1000 },
  { id: 3, x: 20, y: 80, size: 'w-1 h-1', delay: 2000 },
  { id: 4, x: 90, y: 70, size: 'w-2 h-2', delay: 1500 },
  { id: 5, x: 60, y: 30, size: 'w-1 h-1', delay: 500 },
]

const backgroundGradients = [
  'bg-gradient-to-br from-blue-900/40 via-purple-900/40 to-black/60',
  'bg-gradient-to-br from-green-900/40 via-teal-900/40 to-black/60',
  'bg-gradient-to-br from-orange-900/40 via-red-900/40 to-black/60',
  'bg-gradient-to-br from-purple-900/40 via-pink-900/40 to-black/60',
  'bg-gradient-to-br from-yellow-900/40 via-orange-900/40 to-black/60',
]

export function HeroSection() {
  const [isVisible, setIsVisible] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)

  useEffect(() => {
    setIsVisible(true)
  }, [])

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentImageIndex((prev) => (prev + 1) % backgroundGradients.length)
    }, 4000) // Change gradient every 4 seconds

    return () => clearInterval(interval)
  }, [])

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0">
        {/* Background Gradient Slideshow */}
        <div className="absolute inset-0 overflow-hidden">
          {backgroundGradients.map((gradient, index) => (
            <div
              key={index}
              className={`absolute inset-0 transition-opacity duration-2000 ${gradient} ${
                index === currentImageIndex ? 'opacity-100' : 'opacity-0'
              }`}
            />
          ))}
        </div>

        {/* Gradient Background Overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-gray-900/90 via-black/80 to-gray-900/90" />

        {/* Animated Grid */}
        <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px] [mask-image:radial-gradient(ellipse_80%_50%_at_50%_0%,#000_70%,transparent_110%)]" />

        {/* Floating Particles */}
        {floatingElements.map((element) => (
          <div
            key={element.id}
            className={`absolute ${element.size} bg-yellow-400/20 rounded-full animate-pulse`}
            style={{
              left: `${element.x}%`,
              top: `${element.y}%`,
              animationDelay: `${element.delay}ms`,
              animationDuration: '3s'
            }}
          />
        ))}

        {/* Radial Gradient Overlay */}
        <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,transparent_0%,rgba(0,0,0,0.6)_70%)]" />
      </div>

      {/* Content */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <div className={`transition-all duration-1000 ${isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'}`}>
          {/* Badge */}
          <div className="inline-flex items-center px-4 py-2 bg-yellow-500/10 border border-yellow-500/20 rounded-full text-yellow-400 text-sm font-medium mb-8">
            <TrophyIcon className="w-4 h-4 mr-2" />
            #1 Premium Forex Bot Store
          </div>

          {/* Main Heading */}
          <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold text-white mb-6 leading-tight">
            Premium Bots,{' '}
            <span className="relative inline-block">
              <span className="bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent">
                Special Prices
              </span>
              <span className="absolute inset-0 bg-gradient-to-r from-yellow-400 via-orange-500 to-red-500 bg-clip-text text-transparent animate-pulse opacity-75 blur-sm">
                Special Prices
              </span>
            </span>
          </h1>

          {/* Subtitle */}
          <p className="text-xl md:text-2xl text-gray-300 mb-8 max-w-4xl mx-auto leading-relaxed">
            Get premium Forex Expert Advisors and Indicators at{' '}
            <span className="text-yellow-400 font-semibold">unbeatable special prices</span>.
            Professional trading bots and tools that deliver real results for serious traders.
          </p>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
            <Link href="/products">
              <Button variant="premium" size="xl" className="group">
                Shop Premium Bots
                <ArrowRightIcon className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
              </Button>
            </Link>
            
            <Button variant="glass" size="xl" className="group">
              <PlayIcon className="w-5 h-5 mr-2" />
              Watch Demo
            </Button>
          </div>

          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
            {stats.map((stat, index) => (
              <div
                key={stat.label}
                className={`glass p-6 rounded-xl border border-white/10 transition-all duration-500 hover:border-yellow-500/30 hover:shadow-glow ${
                  isVisible ? 'opacity-100 translate-y-0' : 'opacity-0 translate-y-10'
                }`}
                style={{ transitionDelay: `${index * 200}ms` }}
              >
                <div className="flex items-center justify-center mb-4">
                  <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                    <stat.icon className="w-6 h-6 text-black" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-white mb-2">{stat.value}</div>
                <div className="text-gray-400 text-sm">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Scroll Indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
          <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse" />
        </div>
      </div>

      {/* Side Decorations */}
      <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-32 bg-gradient-to-b from-transparent via-yellow-400 to-transparent opacity-50" />
      <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-32 bg-gradient-to-b from-transparent via-yellow-400 to-transparent opacity-50" />
    </section>
  )
}
