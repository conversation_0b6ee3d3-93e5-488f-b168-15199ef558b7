import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    // Clear all NextAuth cookies
    const cookieStore = cookies()
    
    // Get all cookies and clear NextAuth related ones
    const allCookies = cookieStore.getAll()
    const response = NextResponse.json({ success: true, message: 'Session cleared' })
    
    // Clear NextAuth cookies
    allCookies.forEach(cookie => {
      if (cookie.name.startsWith('next-auth') || cookie.name.startsWith('__Secure-next-auth')) {
        response.cookies.delete(cookie.name)
      }
    })
    
    return response
  } catch (error) {
    console.error('Error clearing session:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to clear session'
    }, { status: 500 })
  }
}
