import { NextRequest, NextResponse } from 'next/server'
import { createUser } from '@/lib/auth-utils'
import { sendWelcomeEmail } from '@/lib/email'
import { z } from 'zod'

const registerSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(8, 'Password must be at least 8 characters'),
  firstName: z.string().min(1, 'First name is required'),
  lastName: z.string().min(1, 'Last name is required'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const validatedData = registerSchema.parse(body)
    
    // Create user
    const user = await createUser(validatedData)

    // Send welcome email
    try {
      await sendWelcomeEmail(user.email, {
        firstName: user.firstName,
        email: user.email,
        loginUrl: `${process.env.NEXTAUTH_URL || process.env.SITE_URL}/auth/signin`
      })
      console.log(`Welcome email sent to: ${user.email}`)
    } catch (emailError) {
      console.error('Failed to send welcome email:', emailError)
      // Don't fail registration if email fails
    }

    return NextResponse.json({
      success: true,
      message: 'User created successfully',
      data: user
    }, { status: 201 })
    
  } catch (error: any) {
    console.error('Registration error:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }
    
    return NextResponse.json({
      success: false,
      message: error.message || 'Registration failed'
    }, { status: 400 })
  }
}
