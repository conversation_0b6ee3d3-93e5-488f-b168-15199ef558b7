#!/usr/bin/env node

/**
 * Test script for Stripe webhook functionality
 * Run this script to test if your webhook endpoint is working correctly
 */

const http = require('http');

const testWebhookPayload = {
  id: 'evt_test_webhook',
  object: 'event',
  api_version: '2024-06-20',
  created: Math.floor(Date.now() / 1000),
  data: {
    object: {
      id: 'pi_test_payment_intent',
      object: 'payment_intent',
      amount: 2000,
      currency: 'usd',
      status: 'succeeded',
      metadata: {
        orderId: 'test_order_123'
      }
    }
  },
  livemode: false,
  pending_webhooks: 1,
  request: {
    id: 'req_test_request',
    idempotency_key: null
  },
  type: 'payment_intent.succeeded'
};

const postData = JSON.stringify(testWebhookPayload);

const options = {
  hostname: 'localhost',
  port: 3000,
  path: '/api/webhooks/stripe',
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Content-Length': Buffer.byteLength(postData),
    'stripe-signature': 'test_signature' // This will fail signature validation, but we can test the endpoint
  }
};

console.log('Testing Stripe webhook endpoint...');
console.log('URL: http://localhost:3000/api/webhooks/stripe');
console.log('');

const req = http.request(options, (res) => {
  console.log(`Status Code: ${res.statusCode}`);
  console.log(`Headers:`, res.headers);
  console.log('');

  let data = '';
  res.on('data', (chunk) => {
    data += chunk;
  });

  res.on('end', () => {
    console.log('Response Body:');
    try {
      const jsonResponse = JSON.parse(data);
      console.log(JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      console.log(data);
    }
    
    console.log('');
    if (res.statusCode === 400 && data.includes('signature')) {
      console.log('✅ Webhook endpoint is working! (Signature validation failed as expected)');
      console.log('');
      console.log('Next steps:');
      console.log('1. Run: stripe listen --forward-to localhost:3000/api/webhooks/stripe');
      console.log('2. Copy the webhook secret from the output');
      console.log('3. Update STRIPE_WEBHOOK_SECRET in your .env file');
      console.log('4. Restart your development server');
      console.log('5. Test with: stripe trigger payment_intent.succeeded');
    } else if (res.statusCode === 200) {
      console.log('✅ Webhook endpoint is working perfectly!');
    } else {
      console.log('❌ Webhook endpoint may have issues. Check your server logs.');
    }
  });
});

req.on('error', (e) => {
  console.error('❌ Error testing webhook endpoint:');
  console.error(e.message);
  console.log('');
  console.log('Make sure your development server is running on http://localhost:3000');
});

req.write(postData);
req.end();
