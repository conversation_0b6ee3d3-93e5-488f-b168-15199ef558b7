'use client'

import { useEffect, useState } from 'react'
import { StarIcon } from '@heroicons/react/24/solid'
import Image from 'next/image'

interface Testimonial {
  id: number
  name: string
  role: string
  avatar: string
  rating: number
  comment: string
  initials: string
  gradientFrom: string
  gradientTo: string
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Professional Trader',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "These EAs have completely transformed my trading strategy. I've seen consistent profits for the past 6 months. The quality is exceptional!",
    initials: 'JM',
    gradientFrom: 'from-blue-500',
    gradientTo: 'to-purple-600'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Forex Enthusiast',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "Amazing customer support and high-quality indicators. The download process is seamless and the tools work perfectly with MT4/MT5.",
    initials: 'SK',
    gradientFrom: 'from-green-500',
    gradientTo: 'to-blue-600'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Day Trader',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "I've tried many platforms, but this one stands out. The EAs are profitable and the indicators are accurate. Highly recommended!",
    initials: 'DR',
    gradientFrom: 'from-purple-500',
    gradientTo: 'to-pink-600'
  },
  {
    id: 4,
    name: 'Michael Chen',
    role: 'Algorithmic Trader',
    avatar: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "The AI-powered EAs are incredible. They adapt to market conditions better than anything I've used before. ROI has been outstanding.",
    initials: 'MC',
    gradientFrom: 'from-orange-500',
    gradientTo: 'to-red-600'
  },
  {
    id: 5,
    name: 'Emma Thompson',
    role: 'Swing Trader',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "Perfect for swing trading strategies. The indicators are precise and the EAs handle risk management beautifully. Worth every penny!",
    initials: 'ET',
    gradientFrom: 'from-teal-500',
    gradientTo: 'to-cyan-600'
  },
  {
    id: 6,
    name: 'Robert Johnson',
    role: 'Institutional Trader',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "Using these tools for our institutional trading desk. The performance metrics are impressive and the reliability is unmatched.",
    initials: 'RJ',
    gradientFrom: 'from-indigo-500',
    gradientTo: 'to-purple-600'
  },
  {
    id: 7,
    name: 'Lisa Wang',
    role: 'Scalping Specialist',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "The scalping EAs are phenomenal! Lightning-fast execution and consistent small profits that add up to significant gains.",
    initials: 'LW',
    gradientFrom: 'from-pink-500',
    gradientTo: 'to-rose-600'
  },
  {
    id: 8,
    name: 'Ahmed Hassan',
    role: 'Currency Analyst',
    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "As a currency analyst, I appreciate the sophisticated algorithms. These tools provide insights that give me a real edge in the market.",
    initials: 'AH',
    gradientFrom: 'from-yellow-500',
    gradientTo: 'to-orange-600'
  },
  {
    id: 9,
    name: 'Maria Garcia',
    role: 'Portfolio Manager',
    avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "Managing multiple portfolios is easier with these EAs. They maintain consistent performance across different market conditions.",
    initials: 'MG',
    gradientFrom: 'from-emerald-500',
    gradientTo: 'to-teal-600'
  },
  {
    id: 10,
    name: 'James Wilson',
    role: 'Hedge Fund Manager',
    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "We've integrated several of these EAs into our hedge fund strategies. The risk-adjusted returns have exceeded our expectations significantly.",
    initials: 'JW',
    gradientFrom: 'from-violet-500',
    gradientTo: 'to-purple-600'
  }
]

export function TestimonialSlider() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isHovered, setIsHovered] = useState(false)
  const [visibleTestimonials, setVisibleTestimonials] = useState(3)

  // Responsive testimonials count
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth < 768) {
        setVisibleTestimonials(1)
      } else if (window.innerWidth < 1024) {
        setVisibleTestimonials(2)
      } else {
        setVisibleTestimonials(3)
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Auto-sliding functionality - pause on hover
  useEffect(() => {
    if (isHovered) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => {
        const maxIndex = Math.max(0, testimonials.length - visibleTestimonials)
        return prev >= maxIndex ? 0 : prev + 1
      })
    }, 4000) // Change every 4 seconds

    return () => clearInterval(interval)
  }, [isHovered, visibleTestimonials])



  return (
    <section className="py-20 px-4 bg-gradient-to-b from-gray-900/50 to-gray-900">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-5xl font-bold text-white mb-4">
            What Our Traders Say
          </h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Join thousands of successful traders who trust our premium tools and strategies
          </p>
        </div>

        <div
          className="relative overflow-hidden"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Testimonials Container */}
          <div className="flex transition-transform duration-1000 ease-in-out"
               style={{
                 transform: `translateX(-${currentIndex * (100 / visibleTestimonials)}%)`,
                 width: `${(testimonials.length / visibleTestimonials) * 100}%`
               }}>
            {testimonials.map((testimonial, index) => (
              <div
                key={testimonial.id}
                className="flex-shrink-0 px-4"
                style={{ width: `${100 / testimonials.length}%` }}
              >
                <div className="bg-gradient-to-br from-gray-800/80 to-gray-900/80 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-8 h-full transform transition-all duration-500 hover:scale-105 hover:border-yellow-500/30 hover:shadow-2xl hover:shadow-yellow-500/10 group">
                  {/* Header with Avatar */}
                  <div className="flex items-center mb-6">
                    <div className="relative w-16 h-16 mr-4">
                      <div className="absolute inset-0 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full p-0.5 group-hover:from-yellow-300 group-hover:to-orange-400 transition-all duration-300">
                        <div className="w-full h-full bg-gray-900 rounded-full overflow-hidden relative">
                          <Image
                            src={testimonial.avatar}
                            alt={testimonial.name}
                            width={64}
                            height={64}
                            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-110"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              const parent = target.parentElement
                              if (parent) {
                                parent.innerHTML = `<div class="w-full h-full bg-gradient-to-r ${testimonial.gradientFrom} ${testimonial.gradientTo} rounded-full flex items-center justify-center">
                                  <span class="text-white font-bold text-lg">${testimonial.initials}</span>
                                </div>`
                              }
                            }}
                          />
                        </div>
                      </div>
                    </div>
                    <div>
                      <h4 className="text-white font-bold text-lg group-hover:text-yellow-400 transition-colors duration-300">{testimonial.name}</h4>
                      <p className="text-yellow-400 text-sm font-medium">{testimonial.role}</p>
                    </div>
                  </div>

                  {/* Rating Stars */}
                  <div className="flex mb-6 space-x-1">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <StarIcon key={i} className="w-5 h-5 text-yellow-400 drop-shadow-lg group-hover:text-yellow-300 transition-colors duration-300" />
                    ))}
                  </div>

                  {/* Testimonial Text */}
                  <blockquote className="text-gray-300 leading-relaxed text-base italic group-hover:text-gray-200 transition-colors duration-300">
                    "{testimonial.comment}"
                  </blockquote>

                  {/* Decorative Quote */}
                  <div className="absolute top-6 right-6 text-yellow-400/20 text-6xl font-serif group-hover:text-yellow-400/30 transition-colors duration-300">
                    "
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>


        {/* Auto-sliding indicator */}
        <div className="flex justify-center mt-12">
          <div className="flex items-center space-x-2 text-gray-400 text-sm">
            <div className={`w-2 h-2 bg-yellow-400 rounded-full ${isHovered ? '' : 'animate-pulse'}`}></div>
            <span>{isHovered ? 'Paused' : 'Auto-sliding'} • Hover to pause</span>
          </div>
        </div>
      </div>
    </section>
  )
}
