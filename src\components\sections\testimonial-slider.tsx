'use client'

import { useEffect, useState } from 'react'
import { StarIcon } from '@heroicons/react/24/solid'

interface Testimonial {
  id: number
  name: string
  role: string
  avatar: string
  rating: number
  comment: string
  initials: string
  gradientFrom: string
  gradientTo: string
}

const testimonials: Testimonial[] = [
  {
    id: 1,
    name: '<PERSON>',
    role: 'Professional Trader',
    avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "These EAs have completely transformed my trading strategy. I've seen consistent profits for the past 6 months. The quality is exceptional!",
    initials: 'J<PERSON>',
    gradientFrom: 'from-blue-500',
    gradientTo: 'to-purple-600'
  },
  {
    id: 2,
    name: '<PERSON>',
    role: 'Forex Enthusiast',
    avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "Amazing customer support and high-quality indicators. The download process is seamless and the tools work perfectly with MT4/MT5.",
    initials: 'SK',
    gradientFrom: 'from-green-500',
    gradientTo: 'to-blue-600'
  },
  {
    id: 3,
    name: '<PERSON>',
    role: 'Day Trader',
    avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "I've tried many platforms, but this one stands out. The EAs are profitable and the indicators are accurate. Highly recommended!",
    initials: 'DR',
    gradientFrom: 'from-purple-500',
    gradientTo: 'to-pink-600'
  },
  {
    id: 4,
    name: 'Michael Chen',
    role: 'Algorithmic Trader',
    avatar: 'https://images.unsplash.com/photo-1519345182560-3f2917c472ef?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "The AI-powered EAs are incredible. They adapt to market conditions better than anything I've used before. ROI has been outstanding.",
    initials: 'MC',
    gradientFrom: 'from-orange-500',
    gradientTo: 'to-red-600'
  },
  {
    id: 5,
    name: 'Emma Thompson',
    role: 'Swing Trader',
    avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "Perfect for swing trading strategies. The indicators are precise and the EAs handle risk management beautifully. Worth every penny!",
    initials: 'ET',
    gradientFrom: 'from-teal-500',
    gradientTo: 'to-cyan-600'
  },
  {
    id: 6,
    name: 'Robert Johnson',
    role: 'Institutional Trader',
    avatar: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "Using these tools for our institutional trading desk. The performance metrics are impressive and the reliability is unmatched.",
    initials: 'RJ',
    gradientFrom: 'from-indigo-500',
    gradientTo: 'to-purple-600'
  },
  {
    id: 7,
    name: 'Lisa Wang',
    role: 'Scalping Specialist',
    avatar: 'https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "The scalping EAs are phenomenal! Lightning-fast execution and consistent small profits that add up to significant gains.",
    initials: 'LW',
    gradientFrom: 'from-pink-500',
    gradientTo: 'to-rose-600'
  },
  {
    id: 8,
    name: 'Ahmed Hassan',
    role: 'Currency Analyst',
    avatar: 'https://images.unsplash.com/photo-1506794778202-cad84cf45f1d?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "As a currency analyst, I appreciate the sophisticated algorithms. These tools provide insights that give me a real edge in the market.",
    initials: 'AH',
    gradientFrom: 'from-yellow-500',
    gradientTo: 'to-orange-600'
  },
  {
    id: 9,
    name: 'Maria Garcia',
    role: 'Portfolio Manager',
    avatar: 'https://images.unsplash.com/photo-1487412720507-e7ab37603c6f?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "Managing multiple portfolios is easier with these EAs. They maintain consistent performance across different market conditions.",
    initials: 'MG',
    gradientFrom: 'from-emerald-500',
    gradientTo: 'to-teal-600'
  },
  {
    id: 10,
    name: 'James Wilson',
    role: 'Hedge Fund Manager',
    avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?w=150&h=150&fit=crop&crop=face',
    rating: 5,
    comment: "We've integrated several of these EAs into our hedge fund strategies. The risk-adjusted returns have exceeded our expectations significantly.",
    initials: 'JW',
    gradientFrom: 'from-violet-500',
    gradientTo: 'to-purple-600'
  }
]

export function TestimonialSlider() {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isHovered, setIsHovered] = useState(false)

  // Auto-sliding functionality - pause on hover
  useEffect(() => {
    if (isHovered) return

    const interval = setInterval(() => {
      setCurrentIndex((prev) => (prev + 1) % testimonials.length)
    }, 4000) // Change every 4 seconds

    return () => clearInterval(interval)
  }, [isHovered])



  // Get current testimonials to display (3 at a time)
  const getVisibleTestimonials = () => {
    const visible = []
    for (let i = 0; i < 3; i++) {
      const index = (currentIndex + i) % testimonials.length
      visible.push(testimonials[index])
    }
    return visible
  }

  return (
    <section className="py-12 sm:py-16 lg:py-20 px-4 bg-gradient-to-b from-gray-900/50 to-gray-900">
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12 sm:mb-16">
          <h2 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-white mb-3 sm:mb-4">
            What Our Traders Say
          </h2>
          <p className="text-base sm:text-lg lg:text-xl text-gray-300 max-w-2xl mx-auto">
            Join thousands of successful traders who trust our premium tools and strategies
          </p>
        </div>

        <div
          className="relative"
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          {/* Testimonials Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
            {getVisibleTestimonials().map((testimonial, index) => (
              <div
                key={`${testimonial.id}-${currentIndex}-${index}`}
                className="bg-gradient-to-br from-gray-800/90 to-gray-900/90 backdrop-blur-xl border border-gray-700/50 rounded-2xl p-6 sm:p-8 transform transition-all duration-700 hover:scale-105 hover:border-yellow-500/40 hover:shadow-2xl hover:shadow-yellow-500/20 group animate-fade-in-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                {/* Rating Stars */}
                <div className="flex justify-center mb-4 sm:mb-6 space-x-1">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <StarIcon key={i} className="w-5 h-5 sm:w-6 sm:h-6 text-yellow-400 drop-shadow-lg group-hover:text-yellow-300 transition-colors duration-300" />
                  ))}
                </div>

                {/* Testimonial Text */}
                <blockquote className="text-gray-300 leading-relaxed text-base sm:text-lg italic text-center mb-6 sm:mb-8 group-hover:text-gray-200 transition-colors duration-300 min-h-[100px] sm:min-h-[120px] flex items-center">
                  <span className="relative">
                    <span className="absolute -top-3 -left-1 sm:-top-4 sm:-left-2 text-yellow-400/40 text-3xl sm:text-4xl font-serif">"</span>
                    {testimonial.comment}
                    <span className="absolute -bottom-5 -right-1 sm:-bottom-6 sm:-right-2 text-yellow-400/40 text-3xl sm:text-4xl font-serif">"</span>
                  </span>
                </blockquote>

                {/* Author Info */}
                <div className="text-center border-t border-gray-700/50 pt-4 sm:pt-6">
                  <h4 className="text-white font-bold text-lg sm:text-xl mb-1 group-hover:text-yellow-400 transition-colors duration-300">
                    {testimonial.name}
                  </h4>
                  <p className="text-yellow-400/80 text-xs sm:text-sm font-medium uppercase tracking-wider">
                    {testimonial.role}
                  </p>
                </div>

                {/* Decorative Elements */}
                <div className="absolute top-3 right-3 sm:top-4 sm:right-4 w-6 h-6 sm:w-8 sm:h-8 bg-gradient-to-br from-yellow-400/20 to-orange-500/20 rounded-full group-hover:from-yellow-400/30 group-hover:to-orange-500/30 transition-all duration-300"></div>
                <div className="absolute bottom-3 left-3 sm:bottom-4 sm:left-4 w-4 h-4 sm:w-6 sm:h-6 bg-gradient-to-br from-yellow-400/10 to-orange-500/10 rounded-full group-hover:from-yellow-400/20 group-hover:to-orange-500/20 transition-all duration-300"></div>
              </div>
            ))}
          </div>
        </div>


        {/* Auto-sliding indicator */}
        <div className="flex justify-center mt-12">
          <div className="flex items-center space-x-2 text-gray-400 text-sm">
            <div className={`w-2 h-2 bg-yellow-400 rounded-full ${isHovered ? '' : 'animate-pulse'}`}></div>
            <span>{isHovered ? 'Paused' : 'Auto-sliding'} • Hover to pause</span>
          </div>
        </div>

        {/* Progress Dots */}
        <div className="flex justify-center mt-8 space-x-2">
          {testimonials.map((_, index) => (
            <div
              key={index}
              className={`w-2 h-2 rounded-full transition-all duration-300 ${
                index === currentIndex
                  ? 'bg-yellow-400 w-6'
                  : 'bg-gray-600 hover:bg-gray-500'
              }`}
            />
          ))}
        </div>
      </div>
    </section>
  )
}
