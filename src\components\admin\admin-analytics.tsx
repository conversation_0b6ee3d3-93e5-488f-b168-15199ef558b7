'use client'

import { useEffect, useState } from 'react'
import {
  ChartBarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  EyeIcon,
  ShoppingCartIcon,
  CurrencyDollarIcon
} from '@heroicons/react/24/outline'

interface AnalyticsData {
  totalViews: number
  totalSales: number
  totalRevenue: number
  conversionRate: number
  topProducts: Array<{
    name: string
    sales: number
    revenue: number
  }>
  salesTrend: Array<{
    date: string
    sales: number
    revenue: number
  }>
}

export function AdminAnalytics() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('7d')

  useEffect(() => {
    const fetchAnalytics = async () => {
      try {
        const response = await fetch(`/api/admin/analytics?range=${timeRange}`)
        if (response.ok) {
          const data = await response.json()
          setAnalytics(data.data)
        }
      } catch (error) {
        console.error('Error fetching analytics:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchAnalytics()
  }, [timeRange])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  // Mock data for demonstration
  const mockAnalytics: AnalyticsData = {
    totalViews: 12543,
    totalSales: 89,
    totalRevenue: 4567.89,
    conversionRate: 7.1,
    topProducts: [
      { name: 'TrippaTrading AI EA MT4 EA', sales: 23, revenue: 1147.77 },
      { name: 'Quantum Queen MT5 EA', sales: 18, revenue: 1079.82 },
      { name: 'Neural Network EA MT5 EA', sales: 15, revenue: 974.85 },
      { name: 'Complete Scalping System Trading Systems', sales: 12, revenue: 959.88 },
      { name: 'Smart Money Concepts MT5 Indicators', sales: 11, revenue: 494.89 },
    ],
    salesTrend: [
      { date: '2024-01-01', sales: 12, revenue: 567.89 },
      { date: '2024-01-02', sales: 15, revenue: 723.45 },
      { date: '2024-01-03', sales: 8, revenue: 432.10 },
      { date: '2024-01-04', sales: 18, revenue: 891.23 },
      { date: '2024-01-05', sales: 22, revenue: 1123.45 },
      { date: '2024-01-06', sales: 14, revenue: 678.90 },
      { date: '2024-01-07', sales: 19, revenue: 945.67 },
    ]
  }

  const data = analytics || mockAnalytics

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          {[
            { value: '7d', label: '7 Days' },
            { value: '30d', label: '30 Days' },
            { value: '90d', label: '90 Days' },
            { value: '1y', label: '1 Year' },
          ].map((range) => (
            <button
              key={range.value}
              onClick={() => setTimeRange(range.value)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                timeRange === range.value
                  ? 'bg-yellow-400 text-black'
                  : 'bg-white/10 text-gray-300 hover:bg-white/20'
              }`}
            >
              {range.label}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <EyeIcon className="h-8 w-8 text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total Views</p>
              <p className="text-2xl font-bold text-white">{data.totalViews.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ShoppingCartIcon className="h-8 w-8 text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total Sales</p>
              <p className="text-2xl font-bold text-white">{data.totalSales}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total Revenue</p>
              <p className="text-2xl font-bold text-white">${data.totalRevenue.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ArrowTrendingUpIcon className="h-8 w-8 text-purple-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Conversion Rate</p>
              <p className="text-2xl font-bold text-white">{data.conversionRate}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Top Products */}
      <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
        <h3 className="text-lg font-medium text-white mb-4">Top Performing Products</h3>
        <div className="space-y-4">
          {data.topProducts.map((product, index) => (
            <div key={index} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className="flex-shrink-0 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                  <span className="text-sm font-bold text-black">{index + 1}</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-white">{product.name}</p>
                  <p className="text-xs text-gray-400">{product.sales} sales</p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-sm font-medium text-white">${product.revenue.toFixed(2)}</p>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Sales Trend Chart Placeholder */}
      <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
        <h3 className="text-lg font-medium text-white mb-4">Sales Trend</h3>
        <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-600 rounded-lg">
          <div className="text-center">
            <ChartBarIcon className="h-12 w-12 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-400">Chart visualization would go here</p>
            <p className="text-sm text-gray-500">Integration with charting library needed</p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AdminAnalytics
