'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  BugAntIcon, 
  HeartIcon, 
  DocumentTextIcon,
  TrashIcon,
  ArrowPathIcon 
} from '@heroicons/react/24/outline'

interface DebugLog {
  timestamp: string
  level: 'info' | 'warn' | 'error' | 'debug'
  category: string
  message: string
  data?: any
}

interface HealthStatus {
  status: 'healthy' | 'unhealthy'
  checks: {
    environment: {
      isValid: boolean
      missing: string[]
      missingOptional: string[]
    }
    database: {
      connected: boolean
      error: string | null
    }
  }
  system: any
}

export function DebugPanel() {
  const [logs, setLogs] = useState<DebugLog[]>([])
  const [health, setHealth] = useState<HealthStatus | null>(null)
  const [loading, setLoading] = useState(false)
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedLevel, setSelectedLevel] = useState<string>('all')

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null
  }

  const fetchLogs = async () => {
    try {
      setLoading(true)
      const params = new URLSearchParams()
      if (selectedCategory !== 'all') params.set('category', selectedCategory)
      if (selectedLevel !== 'all') params.set('level', selectedLevel)
      params.set('limit', '50')

      const response = await fetch(`/api/debug/logs?${params}`)
      const data = await response.json()
      
      if (data.success) {
        setLogs(data.data.logs)
      }
    } catch (error) {
      console.error('Failed to fetch logs:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchHealth = async () => {
    try {
      const response = await fetch('/api/debug/health')
      const data = await response.json()
      
      if (data.success) {
        setHealth(data.data)
      }
    } catch (error) {
      console.error('Failed to fetch health:', error)
    }
  }

  const clearLogs = async () => {
    try {
      await fetch('/api/debug/logs', { method: 'DELETE' })
      setLogs([])
    } catch (error) {
      console.error('Failed to clear logs:', error)
    }
  }

  useEffect(() => {
    fetchLogs()
    fetchHealth()
  }, [selectedCategory, selectedLevel])

  const getLevelColor = (level: string) => {
    switch (level) {
      case 'error': return 'bg-red-500'
      case 'warn': return 'bg-yellow-500'
      case 'info': return 'bg-blue-500'
      case 'debug': return 'bg-purple-500'
      default: return 'bg-gray-500'
    }
  }

  const categories = ['all', ...new Set(logs.map(log => log.category))]
  const levels = ['all', 'error', 'warn', 'info', 'debug']

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-96 bg-gray-900 border border-gray-700 rounded-lg shadow-xl z-50">
      <div className="p-4 border-b border-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <BugAntIcon className="h-5 w-5 text-yellow-400" />
            <h3 className="text-white font-medium">Debug Panel</h3>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              onClick={fetchHealth}
              size="sm"
              variant="ghost"
              className="p-1"
            >
              <HeartIcon className="h-4 w-4" />
            </Button>
            <Button
              onClick={fetchLogs}
              size="sm"
              variant="ghost"
              className="p-1"
            >
              <ArrowPathIcon className="h-4 w-4" />
            </Button>
            <Button
              onClick={clearLogs}
              size="sm"
              variant="ghost"
              className="p-1"
            >
              <TrashIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div className="p-4 space-y-4 max-h-80 overflow-y-auto">
        {/* Health Status */}
        {health && (
          <Card className="bg-gray-800 border-gray-700">
            <CardHeader className="pb-2">
              <CardTitle className="text-sm text-white flex items-center">
                <HeartIcon className="h-4 w-4 mr-2" />
                System Health
                <Badge 
                  className={`ml-2 ${health.status === 'healthy' ? 'bg-green-500' : 'bg-red-500'}`}
                >
                  {health.status}
                </Badge>
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-400">Database:</span>
                  <span className={health.checks.database.connected ? 'text-green-400' : 'text-red-400'}>
                    {health.checks.database.connected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-400">Environment:</span>
                  <span className={health.checks.environment.isValid ? 'text-green-400' : 'text-red-400'}>
                    {health.checks.environment.isValid ? 'Valid' : 'Invalid'}
                  </span>
                </div>
                {health.checks.environment.missing.length > 0 && (
                  <div className="text-red-400 text-xs">
                    Missing: {health.checks.environment.missing.join(', ')}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Filters */}
        <div className="flex space-x-2">
          <select
            value={selectedCategory}
            onChange={(e) => setSelectedCategory(e.target.value)}
            className="text-xs bg-gray-800 text-white border border-gray-600 rounded px-2 py-1"
          >
            {categories.map(cat => (
              <option key={cat} value={cat}>{cat}</option>
            ))}
          </select>
          <select
            value={selectedLevel}
            onChange={(e) => setSelectedLevel(e.target.value)}
            className="text-xs bg-gray-800 text-white border border-gray-600 rounded px-2 py-1"
          >
            {levels.map(level => (
              <option key={level} value={level}>{level}</option>
            ))}
          </select>
        </div>

        {/* Logs */}
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <DocumentTextIcon className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-white">Recent Logs ({logs.length})</span>
          </div>
          
          {loading ? (
            <div className="text-center py-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-yellow-400 mx-auto"></div>
            </div>
          ) : logs.length === 0 ? (
            <div className="text-center py-4 text-gray-400 text-sm">
              No logs found
            </div>
          ) : (
            <div className="space-y-1 max-h-40 overflow-y-auto">
              {logs.slice(-20).reverse().map((log, index) => (
                <div key={index} className="text-xs bg-gray-800 p-2 rounded border border-gray-700">
                  <div className="flex items-center justify-between mb-1">
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${getLevelColor(log.level)}`}></div>
                      <span className="text-gray-300">{log.category}</span>
                    </div>
                    <span className="text-gray-500">
                      {new Date(log.timestamp).toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text-gray-200">{log.message}</div>
                  {log.data && (
                    <details className="mt-1">
                      <summary className="text-gray-400 cursor-pointer">Data</summary>
                      <pre className="text-gray-500 text-xs mt-1 overflow-auto">
                        {JSON.stringify(log.data, null, 2)}
                      </pre>
                    </details>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
