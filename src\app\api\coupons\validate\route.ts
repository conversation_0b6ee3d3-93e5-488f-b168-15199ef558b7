import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const validateCouponSchema = z.object({
  code: z.string().min(1, 'Coupon code is required'),
  subtotal: z.number().min(0, 'Subtotal must be positive'),
  userId: z.string().optional()
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { code, subtotal, userId } = validateCouponSchema.parse(body)

    // Find the coupon
    const coupon = await prisma.coupon.findUnique({
      where: { code: code.toUpperCase() },
      include: {
        couponUsages: {
          where: userId ? { userId } : undefined
        }
      }
    })

    if (!coupon) {
      return NextResponse.json({
        success: false,
        message: 'Invalid coupon code'
      }, { status: 400 })
    }

    // Check if coupon is active
    if (!coupon.isActive) {
      return NextResponse.json({
        success: false,
        message: 'This coupon is no longer active'
      }, { status: 400 })
    }

    // Check if coupon has started
    if (coupon.startsAt && new Date() < coupon.startsAt) {
      return NextResponse.json({
        success: false,
        message: 'This coupon is not yet valid'
      }, { status: 400 })
    }

    // Check if coupon has expired
    if (coupon.expiresAt && new Date() > coupon.expiresAt) {
      return NextResponse.json({
        success: false,
        message: 'This coupon has expired'
      }, { status: 400 })
    }

    // Check usage limit
    if (coupon.usageLimit && coupon.usageCount >= coupon.usageLimit) {
      return NextResponse.json({
        success: false,
        message: 'This coupon has reached its usage limit'
      }, { status: 400 })
    }

    // Check per-user usage limit
    if (userId && coupon.userUsageLimit) {
      const userUsageCount = coupon.couponUsages.length
      if (userUsageCount >= coupon.userUsageLimit) {
        return NextResponse.json({
          success: false,
          message: 'You have already used this coupon the maximum number of times'
        }, { status: 400 })
      }
    }

    // Check minimum amount
    if (coupon.minimumAmount && subtotal < Number(coupon.minimumAmount)) {
      return NextResponse.json({
        success: false,
        message: `Minimum order amount of $${coupon.minimumAmount} required for this coupon`
      }, { status: 400 })
    }

    // Calculate discount
    let discountAmount = 0
    if (coupon.type === 'FIXED_AMOUNT') {
      discountAmount = Number(coupon.value)
    } else if (coupon.type === 'PERCENTAGE') {
      discountAmount = (subtotal * Number(coupon.value)) / 100
      
      // Apply maximum discount limit if set
      if (coupon.maximumDiscount && discountAmount > Number(coupon.maximumDiscount)) {
        discountAmount = Number(coupon.maximumDiscount)
      }
    }

    // Ensure discount doesn't exceed subtotal
    discountAmount = Math.min(discountAmount, subtotal)

    return NextResponse.json({
      success: true,
      data: {
        coupon: {
          id: coupon.id,
          code: coupon.code,
          name: coupon.name,
          type: coupon.type,
          value: coupon.value
        },
        discountAmount,
        finalAmount: subtotal - discountAmount
      }
    })

  } catch (error: any) {
    console.error('Error validating coupon:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to validate coupon'
    }, { status: 500 })
  }
}
