import { NextAuthOptions } from 'next-auth'
import { PrismaAdapter } from '@next-auth/prisma-adapter'
import CredentialsProvider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import { UserRole } from '@prisma/client'

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          throw new Error('Invalid credentials')
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials.email
          }
        })

        if (!user || !user.password) {
          throw new Error('Invalid credentials')
        }

        const isPasswordValid = await bcrypt.compare(
          credentials.password,
          user.password
        )

        if (!isPasswordValid) {
          throw new Error('Invalid credentials')
        }

        if (!user.isActive) {
          throw new Error('Account is deactivated')
        }

        return {
          id: user.id,
          email: user.email,
          name: `${user.firstName} ${user.lastName}`.trim(),
          image: user.avatar,
          role: user.role,
        }
      }
    }),
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  jwt: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
    error: '/auth/error',
  },
  callbacks: {
    async jwt({ token, user, account }) {
      try {
        if (user) {
          token.role = user.role
          token.id = user.id
        }

        // Handle OAuth account linking
        if (account && account.provider !== 'credentials') {
          const existingUser = await prisma.user.findUnique({
            where: { email: token.email! }
          })

          if (existingUser) {
            token.role = existingUser.role
            token.id = existingUser.id
          }
        }

        return token
      } catch (error) {
        console.error('JWT callback error:', error)
        // Return a minimal token to prevent session corruption
        return {
          email: token.email,
          name: token.name,
          picture: token.picture,
        }
      }
    },
    async session({ session, token }) {
      try {
        if (token) {
          session.user.id = token.id as string
          session.user.role = token.role as UserRole
        }
        return session
      } catch (error) {
        console.error('Session callback error:', error)
        // Return minimal session to prevent errors
        return {
          user: {
            email: session.user?.email,
            name: session.user?.name,
            image: session.user?.image,
          },
          expires: session.expires,
        }
      }
    },
    async signIn({ user, account, profile }) {
      if (account?.provider === 'google') {
        try {
          const existingUser = await prisma.user.findUnique({
            where: { email: user.email! }
          })

          if (!existingUser) {
            // Create new user from Google profile
            await prisma.user.create({
              data: {
                email: user.email!,
                firstName: profile?.given_name || user.name?.split(' ')[0] || '',
                lastName: profile?.family_name || user.name?.split(' ').slice(1).join(' ') || '',
                avatar: user.image,
                emailVerified: new Date(),
                role: UserRole.CUSTOMER,
              }
            })
          }
        } catch (error) {
          console.error('Error creating user:', error)
          return false
        }
      }
      return true
    },
  },
  events: {
    async signIn({ user, account, isNewUser }) {
      if (isNewUser && user.email) {
        console.log(`New user signed up: ${user.email}`)

        // Send welcome email
        try {
          const { sendWelcomeEmail } = await import('@/lib/email')
          await sendWelcomeEmail(user.email, {
            firstName: user.name?.split(' ')[0] || 'Trader',
            email: user.email,
            loginUrl: `${process.env.NEXTAUTH_URL}/auth/signin`
          })
          console.log(`Welcome email sent to: ${user.email}`)
        } catch (error) {
          console.error('Failed to send welcome email:', error)
        }
      }
    },
  },
  debug: process.env.NODE_ENV === 'development',
}
