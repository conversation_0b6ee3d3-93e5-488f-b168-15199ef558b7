import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'Refund Policy | BotZone',
  description: 'Refund Policy for BotZone - Learn about our refund terms and conditions',
}

export default function RefundPolicyPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 py-20">
      <div className="max-w-4xl mx-auto px-4">
        <div className="trading-card p-8">
          <h1 className="text-4xl font-bold text-white mb-8">Refund Policy</h1>
          <div className="text-gray-300 space-y-6">
            <p className="text-sm text-gray-400">Last updated: {new Date().toLocaleDateString()}</p>
            
            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">1. General Policy</h2>
              <p>
                Due to the digital nature of our products (Expert Advisors, Indicators, and Trading Tools), 
                all sales are generally considered final. Once you have downloaded or accessed our digital 
                products, they cannot be "returned" in the traditional sense.
              </p>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">2. Refund Eligibility</h2>
              <p>
                Refunds may be considered in the following exceptional circumstances:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Technical issues that prevent product download or installation</li>
                <li>Product does not match the description provided</li>
                <li>Duplicate purchases made in error</li>
                <li>Unauthorized transactions (subject to investigation)</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">3. Refund Request Process</h2>
              <p>
                To request a refund, you must:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Contact us within 7 days of purchase</li>
                <li>Provide your order number and email address</li>
                <li>Explain the reason for your refund request</li>
                <li>Provide evidence of the issue (if applicable)</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">4. Non-Refundable Circumstances</h2>
              <p>
                Refunds will NOT be provided for:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Trading losses incurred while using our products</li>
                <li>Lack of profitability or expected results</li>
                <li>User error or misunderstanding of product functionality</li>
                <li>Compatibility issues with your trading platform (when requirements are clearly stated)</li>
                <li>Change of mind after successful download</li>
                <li>Requests made after 7 days from purchase date</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">5. Processing Time</h2>
              <p>
                If your refund request is approved:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>We will process the refund within 5-10 business days</li>
                <li>Refunds will be issued to the original payment method</li>
                <li>You will receive an email confirmation once processed</li>
                <li>Bank processing times may vary (typically 3-5 business days)</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">6. Partial Refunds</h2>
              <p>
                In some cases, we may offer partial refunds or store credit as an alternative to 
                full refunds, particularly when:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>The product has been partially used or downloaded</li>
                <li>The issue can be resolved with an alternative product</li>
                <li>The refund request falls outside our standard policy but has merit</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">7. Chargebacks</h2>
              <p>
                If you initiate a chargeback with your bank or credit card company without first 
                contacting us, we reserve the right to:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Suspend your account access</li>
                <li>Pursue collection of any fees incurred</li>
                <li>Report the incident to relevant authorities if fraud is suspected</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">8. Contact for Refunds</h2>
              <p>
                To request a refund or discuss your concerns, please contact us:
              </p>
              <div className="mt-2">
                <p>Email: <EMAIL></p>
                <p>Subject Line: "Refund Request - Order #[Your Order Number]"</p>
                <p>Response Time: Within 24-48 hours</p>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">9. Alternative Solutions</h2>
              <p>
                Before requesting a refund, consider these alternatives:
              </p>
              <ul className="list-disc list-inside mt-2 space-y-1">
                <li>Contact our technical support for installation help</li>
                <li>Review our documentation and tutorials</li>
                <li>Join our community forum for user assistance</li>
                <li>Request a product exchange for a different tool</li>
              </ul>
            </section>

            <section>
              <h2 className="text-2xl font-semibold text-white mb-4">10. Policy Updates</h2>
              <p>
                We reserve the right to update this refund policy at any time. Changes will be 
                posted on this page with an updated revision date. Continued use of our service 
                after changes constitutes acceptance of the new policy.
              </p>
            </section>

            <div className="mt-8 p-4 bg-yellow-900/20 border border-yellow-600 rounded-lg">
              <p className="text-yellow-300 font-semibold">
                Important Note: This refund policy does not affect your statutory rights as a consumer 
                under applicable consumer protection laws.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
