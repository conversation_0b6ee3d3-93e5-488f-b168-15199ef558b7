'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { XMarkIcon } from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Category } from '@prisma/client'
import { toast } from 'react-hot-toast'

interface CategoryModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (category: Category) => void
  category: Category | null
  isNew?: boolean
}

export function CategoryModal({
  isOpen,
  onClose,
  onSave,
  category,
  isNew = false
}: CategoryModalProps) {
  const [loading, setLoading] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    slug: '',
    description: '',
    sortOrder: 0
  })

  useEffect(() => {
    if (category) {
      setFormData({
        name: category.name,
        slug: category.slug,
        description: category.description || '',
        sortOrder: category.sortOrder
      })
    } else if (isNew) {
      setFormData({
        name: '',
        slug: '',
        description: '',
        sortOrder: 0
      })
    }
  }, [category, isNew])

  const generateSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    
    if (name === 'name') {
      setFormData(prev => ({
        ...prev,
        [name]: value,
        slug: generateSlug(value)
      }))
    } else if (name === 'sortOrder') {
      setFormData(prev => ({
        ...prev,
        [name]: parseInt(value) || 0
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }))
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const endpoint = isNew ? '/api/admin/categories' : `/api/admin/categories/${category?.id}`
      const method = isNew ? 'POST' : 'PUT'

      const response = await fetch(endpoint, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      const data = await response.json()

      if (response.ok) {
        toast.success(`Category ${isNew ? 'created' : 'updated'} successfully!`)
        onSave(data.data)
        onClose()
      } else {
        toast.error(data.message || `Failed to ${isNew ? 'create' : 'update'} category`)
      }
    } catch (error) {
      console.error('Error saving category:', error)
      toast.error(`Failed to ${isNew ? 'create' : 'update'} category`)
    } finally {
      setLoading(false)
    }
  }

  const handleClose = () => {
    setFormData({
      name: '',
      slug: '',
      description: '',
      sortOrder: 0
    })
    onClose()
  }

  if (!isOpen) return null

  const modalContent = (
    <div className="fixed inset-0 z-[9999] overflow-y-auto">
      <div className="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
        {/* Backdrop */}
        <div 
          className="fixed inset-0 bg-black bg-opacity-75 transition-opacity"
          onClick={handleClose}
        />
        
        {/* Modal */}
        <div className="relative transform overflow-hidden rounded-lg bg-gray-800 border border-gray-700 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-700">
            <h3 className="text-lg font-semibold text-white">
              {isNew ? 'Create Category' : 'Edit Category'}
            </h3>
            <button
              type="button"
              className="rounded-md text-gray-400 hover:text-gray-300 focus:outline-none"
              onClick={handleClose}
            >
              <XMarkIcon className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            <div>
              <Label htmlFor="name" className="text-white">Category Name</Label>
              <Input
                id="name"
                name="name"
                type="text"
                value={formData.name}
                onChange={handleInputChange}
                required
                className="mt-1 bg-gray-700 border-gray-600 text-white"
                disabled={loading}
                placeholder="Enter category name"
              />
            </div>

            <div>
              <Label htmlFor="slug" className="text-white">Slug</Label>
              <Input
                id="slug"
                name="slug"
                type="text"
                value={formData.slug}
                onChange={handleInputChange}
                required
                className="mt-1 bg-gray-700 border-gray-600 text-white"
                disabled={loading}
                placeholder="category-slug"
              />
              <p className="text-xs text-gray-400 mt-1">
                URL-friendly version of the name. Auto-generated from name.
              </p>
            </div>

            <div>
              <Label htmlFor="description" className="text-white">Description</Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                rows={3}
                className="mt-1 bg-gray-700 border-gray-600 text-white"
                disabled={loading}
                placeholder="Enter category description"
              />
            </div>

            <div>
              <Label htmlFor="sortOrder" className="text-white">Sort Order</Label>
              <Input
                id="sortOrder"
                name="sortOrder"
                type="number"
                value={formData.sortOrder}
                onChange={handleInputChange}
                min="0"
                className="mt-1 bg-gray-700 border-gray-600 text-white"
                disabled={loading}
                placeholder="0"
              />
              <p className="text-xs text-gray-400 mt-1">
                Lower numbers appear first in listings.
              </p>
            </div>

            <div className="flex justify-end space-x-3 pt-4 border-t border-gray-700">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={loading}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={loading}
                className="bg-yellow-500 hover:bg-yellow-600 text-black"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black mr-2"></div>
                    {isNew ? 'Creating...' : 'Updating...'}
                  </>
                ) : (
                  isNew ? 'Create Category' : 'Update Category'
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )

  // Use portal to render modal at document root to avoid z-index issues
  return typeof document !== 'undefined'
    ? createPortal(modalContent, document.body)
    : null
}
