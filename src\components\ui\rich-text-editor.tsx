'use client'

import { useState, useRef, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { 
  BoldIcon, 
  ItalicIcon, 
  UnderlineIcon,
  ListBulletIcon,
  NumberedListIcon,
  PhotoIcon,
  LinkIcon,
  EyeIcon,
  CodeBracketIcon,
  PaintBrushIcon
} from '@heroicons/react/24/outline'

interface RichTextEditorProps {
  value: string
  onChange: (value: string) => void
  placeholder?: string
  className?: string
  disabled?: boolean
  label?: string
}

export function RichTextEditor({
  value,
  onChange,
  placeholder = 'Enter your content...',
  className = '',
  disabled = false,
  label
}: RichTextEditorProps) {
  const [mode, setMode] = useState<'visual' | 'html'>('visual')
  const [htmlValue, setHtmlValue] = useState(value)
  const editorRef = useRef<HTMLDivElement>(null)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  useEffect(() => {
    setHtmlValue(value)
  }, [value])

  const handleCommand = (command: string, value?: string) => {
    if (mode === 'html') return
    
    document.execCommand(command, false, value)
    updateContent()
  }

  const updateContent = () => {
    if (editorRef.current) {
      const content = editorRef.current.innerHTML
      setHtmlValue(content)
      onChange(content)
    }
  }

  const handleHtmlChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    setHtmlValue(newValue)
    onChange(newValue)
  }

  const insertImage = () => {
    const url = prompt('Enter image URL:')
    if (url) {
      handleCommand('insertImage', url)
    }
  }

  const insertLink = () => {
    const url = prompt('Enter link URL:')
    if (url) {
      handleCommand('createLink', url)
    }
  }

  const insertTemplate = (template: string) => {
    if (mode === 'html') {
      const textarea = textareaRef.current
      if (textarea) {
        const start = textarea.selectionStart
        const end = textarea.selectionEnd
        const newValue = htmlValue.substring(0, start) + template + htmlValue.substring(end)
        setHtmlValue(newValue)
        onChange(newValue)
      }
    } else if (editorRef.current) {
      editorRef.current.focus()
      document.execCommand('insertHTML', false, template)
      updateContent()
    }
  }

  const templates = [
    {
      name: 'Feature List',
      html: `
<div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
  <h3 style="color: #333; margin-top: 0;">Key Features</h3>
  <ul style="list-style: none; padding: 0;">
    <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✅ Feature 1</li>
    <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✅ Feature 2</li>
    <li style="padding: 8px 0; border-bottom: 1px solid #eee;">✅ Feature 3</li>
  </ul>
</div>`
    },
    {
      name: 'Performance Box',
      html: `
<div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 12px; margin: 20px 0; text-align: center;">
  <h3 style="margin-top: 0; color: white;">Performance Highlights</h3>
  <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 15px;">
    <div>
      <div style="font-size: 24px; font-weight: bold;">+500%</div>
      <div style="font-size: 14px; opacity: 0.9;">Total Growth</div>
    </div>
    <div>
      <div style="font-size: 24px; font-weight: bold;">77.3%</div>
      <div style="font-size: 14px; opacity: 0.9;">Win Rate</div>
    </div>
    <div>
      <div style="font-size: 24px; font-weight: bold;">36.4%</div>
      <div style="font-size: 14px; opacity: 0.9;">Max Drawdown</div>
    </div>
  </div>
</div>`
    },
    {
      name: 'Image with Caption',
      html: `
<div style="text-align: center; margin: 20px 0;">
  <img src="https://via.placeholder.com/600x400" alt="Product Screenshot" style="max-width: 100%; height: auto; border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);" />
  <p style="font-style: italic; color: #666; margin-top: 10px;">Caption: Replace with your image and description</p>
</div>`
    },
    {
      name: 'Requirements Table',
      html: `
<div style="margin: 20px 0;">
  <h3>System Requirements</h3>
  <table style="width: 100%; border-collapse: collapse; margin-top: 15px;">
    <tr style="background: #f8f9fa;">
      <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Setting</th>
      <th style="padding: 12px; text-align: left; border: 1px solid #ddd;">Recommendation</th>
    </tr>
    <tr>
      <td style="padding: 12px; border: 1px solid #ddd;">Platform</td>
      <td style="padding: 12px; border: 1px solid #ddd;">MetaTrader 5 (MT5)</td>
    </tr>
    <tr style="background: #f8f9fa;">
      <td style="padding: 12px; border: 1px solid #ddd;">Minimum Deposit</td>
      <td style="padding: 12px; border: 1px solid #ddd;">$250 (Recommended: $500+)</td>
    </tr>
    <tr>
      <td style="padding: 12px; border: 1px solid #ddd;">Leverage</td>
      <td style="padding: 12px; border: 1px solid #ddd;">1:100 or higher</td>
    </tr>
  </table>
</div>`
    }
  ]

  return (
    <div className={`space-y-4 ${className}`}>
      {label && <Label className="text-white">{label}</Label>}
      
      {/* Toolbar */}
      <div className="bg-gray-700 rounded-t-lg p-3 border-b border-gray-600">
        <div className="flex flex-wrap items-center gap-2">
          {/* Mode Toggle */}
          <div className="flex bg-gray-600 rounded p-1 mr-4">
            <button
              type="button"
              onClick={() => setMode('visual')}
              className={`px-3 py-1 text-sm rounded ${
                mode === 'visual' ? 'bg-yellow-500 text-black' : 'text-gray-300 hover:text-white'
              }`}
              disabled={disabled}
            >
              <EyeIcon className="h-4 w-4 inline mr-1" />
              Visual
            </button>
            <button
              type="button"
              onClick={() => setMode('html')}
              className={`px-3 py-1 text-sm rounded ${
                mode === 'html' ? 'bg-yellow-500 text-black' : 'text-gray-300 hover:text-white'
              }`}
              disabled={disabled}
            >
              <CodeBracketIcon className="h-4 w-4 inline mr-1" />
              HTML
            </button>
          </div>

          {mode === 'visual' && (
            <>
              {/* Formatting Buttons */}
              <button
                type="button"
                onClick={() => handleCommand('bold')}
                className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded"
                disabled={disabled}
                title="Bold"
              >
                <BoldIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={() => handleCommand('italic')}
                className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded"
                disabled={disabled}
                title="Italic"
              >
                <ItalicIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={() => handleCommand('underline')}
                className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded"
                disabled={disabled}
                title="Underline"
              >
                <UnderlineIcon className="h-4 w-4" />
              </button>

              <div className="w-px h-6 bg-gray-600 mx-2" />

              <button
                type="button"
                onClick={() => handleCommand('insertUnorderedList')}
                className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded"
                disabled={disabled}
                title="Bullet List"
              >
                <ListBulletIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={() => handleCommand('insertOrderedList')}
                className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded"
                disabled={disabled}
                title="Numbered List"
              >
                <NumberedListIcon className="h-4 w-4" />
              </button>

              <div className="w-px h-6 bg-gray-600 mx-2" />

              <button
                type="button"
                onClick={insertImage}
                className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded"
                disabled={disabled}
                title="Insert Image"
              >
                <PhotoIcon className="h-4 w-4" />
              </button>
              <button
                type="button"
                onClick={insertLink}
                className="p-2 text-gray-300 hover:text-white hover:bg-gray-600 rounded"
                disabled={disabled}
                title="Insert Link"
              >
                <LinkIcon className="h-4 w-4" />
              </button>
            </>
          )}

          {/* Templates Dropdown */}
          <div className="relative ml-auto">
            <select
              onChange={(e) => {
                if (e.target.value) {
                  const template = templates.find(t => t.name === e.target.value)
                  if (template) {
                    insertTemplate(template.html)
                  }
                  e.target.value = ''
                }
              }}
              className="bg-gray-600 text-white text-sm rounded px-3 py-1 border border-gray-500"
              disabled={disabled}
            >
              <option value="">Insert Template</option>
              {templates.map(template => (
                <option key={template.name} value={template.name}>
                  {template.name}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Editor */}
      <div className="bg-gray-800 rounded-b-lg border border-gray-600 border-t-0">
        {mode === 'visual' ? (
          <div
            ref={editorRef}
            contentEditable={!disabled}
            onInput={updateContent}
            onBlur={updateContent}
            dangerouslySetInnerHTML={{ __html: htmlValue }}
            className="min-h-[200px] p-4 text-white focus:outline-none"
            style={{
              wordBreak: 'break-word',
              overflowWrap: 'break-word'
            }}
            placeholder={placeholder}
          />
        ) : (
          <Textarea
            ref={textareaRef}
            value={htmlValue}
            onChange={handleHtmlChange}
            rows={12}
            className="bg-gray-800 border-0 text-white font-mono text-sm resize-none rounded-b-lg"
            placeholder="Enter HTML content..."
            disabled={disabled}
          />
        )}
      </div>
    </div>
  )
}
