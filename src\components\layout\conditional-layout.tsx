'use client'

import { usePathname } from 'next/navigation'
import { Header } from './header'
import { Footer } from './footer'

interface ConditionalLayoutProps {
  children: React.ReactNode
}

export function ConditionalLayout({ children }: ConditionalLayoutProps) {
  const pathname = usePathname()
  const isAdminRoute = pathname?.startsWith('/admin')

  if (isAdminRoute) {
    // Admin routes get no header/footer, just the children
    return <>{children}</>
  }

  // Regular routes get the full layout
  return (
    <div className="relative min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black">
      <div className="absolute inset-0 bg-trading-pattern opacity-5"></div>
      <div className="relative z-10">
        <Header />
        <main className="pt-16">
          {children}
        </main>
        <Footer />
      </div>
    </div>
  )
}
