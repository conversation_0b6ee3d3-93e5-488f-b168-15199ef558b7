{"name": "forex-trading-hub", "version": "1.0.0", "description": "Premium Forex EA and Indicators Digital Marketplace", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:seed-minimal": "tsx prisma/seed-minimal.ts", "db:reset": "prisma migrate reset --force && tsx prisma/seed-minimal.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "type-check": "tsc --noEmit"}, "dependencies": {"@aws-sdk/client-s3": "^3.864.0", "@aws-sdk/s3-request-presigner": "^3.864.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "@hookform/resolvers": "^3.3.2", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^5.6.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-checkbox": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-popover": "^1.0.7", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-separator": "^1.0.3", "@radix-ui/react-slider": "^1.1.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.0.3", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.2.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^1.4.11", "@types/node": "^20.9.0", "@types/nodemailer": "^6.4.14", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "autoprefixer": "^10.4.16", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^2.30.0", "framer-motion": "^10.16.5", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.294.0", "multer": "^1.4.5-lts.1", "next": "^14.0.3", "next-auth": "^4.24.5", "next-themes": "^0.2.1", "nodemailer": "^6.9.7", "postcss": "^8.4.31", "prisma": "^5.6.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.48.2", "react-hot-toast": "^2.4.1", "react-query": "^3.39.3", "resend": "^6.0.1", "stripe": "^14.7.0", "swiper": "^11.0.5", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.5", "tailwindcss-animate": "^1.0.7", "typescript": "^5.2.2", "zod": "^3.25.76", "zustand": "^4.4.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.1.5", "@testing-library/react": "^14.1.2", "@testing-library/user-event": "^14.5.1", "@types/jest": "^29.5.8", "eslint": "^8.54.0", "eslint-config-next": "^14.0.3", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "tsx": "^4.6.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "prisma": {"seed": "tsx prisma/seed.ts"}}