import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { sendEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const { templateId, to, variables } = await request.json()

    if (!templateId || !to) {
      return NextResponse.json({
        success: false,
        message: 'Template ID and recipient email are required'
      }, { status: 400 })
    }

    // Get the template from database
    const template = await prisma.emailTemplate.findUnique({
      where: { id: templateId }
    })

    if (!template) {
      return NextResponse.json({
        success: false,
        message: 'Template not found'
      }, { status: 404 })
    }

    if (!template.isActive) {
      return NextResponse.json({
        success: false,
        message: 'Template is not active'
      }, { status: 400 })
    }

    // Replace variables in the template
    let htmlContent = template.htmlContent
    let textContent = template.textContent || ''
    let subject = template.subject

    // Replace variables in all content
    if (variables) {
      for (const [key, value] of Object.entries(variables)) {
        const regex = new RegExp(`\\{\\{${key}\\}\\}`, 'g')
        htmlContent = htmlContent.replace(regex, String(value))
        textContent = textContent.replace(regex, String(value))
        subject = subject.replace(regex, String(value))
      }
    }

    // Send the email
    await sendEmail({
      to,
      subject: `[TEST] ${subject}`,
      html: `
        <div style="background: #f59e0b; color: white; padding: 10px; text-align: center; border-radius: 5px; margin-bottom: 20px;">
          <strong>🧪 TEST EMAIL - ${template.name}</strong>
        </div>
        ${htmlContent}
        <div style="margin-top: 30px; padding: 15px; background: #f3f4f6; border-radius: 5px; border-left: 4px solid #f59e0b;">
          <p style="margin: 0; color: #666; font-size: 12px;">
            <strong>Test Information:</strong><br>
            Template: ${template.name} (${template.type})<br>
            Sent from: Forex Bot Zone Admin Panel<br>
            Environment: ${process.env.NODE_ENV}
          </p>
        </div>
      `,
      text: `[TEST] ${subject}\n\n${textContent}\n\n---\nTest Information:\nTemplate: ${template.name} (${template.type})\nSent from: Forex Bot Zone Admin Panel\nEnvironment: ${process.env.NODE_ENV}`
    })

    // Log the email
    await prisma.emailLog.create({
      data: {
        templateId: template.id,
        to,
        from: 'Forex Bot Zone <<EMAIL>>',
        subject: `[TEST] ${subject}`,
        status: 'SENT',
        sentAt: new Date(),
        metadata: {
          isTest: true,
          variables,
          sentBy: session.user.email
        }
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Test email sent successfully'
    })

  } catch (error: any) {
    console.error('Test email error:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to send test email'
    }, { status: 500 })
  }
}
