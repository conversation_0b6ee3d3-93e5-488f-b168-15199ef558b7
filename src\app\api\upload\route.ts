import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth-utils'
import { 
  uploadFile, 
  generateFileKey, 
  isValidFileType, 
  ALLOWED_FILE_TYPES, 
  MAX_FILE_SIZES 
} from '@/lib/cloudflare-r2'

export async function POST(request: NextRequest) {
  try {
    const user = await requireAuth()
    
    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string // 'product', 'image', 'avatar'
    
    if (!file) {
      return NextResponse.json({
        success: false,
        message: 'No file provided'
      }, { status: 400 })
    }

    if (!type || !['product', 'image', 'avatar'].includes(type)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid file type specified'
      }, { status: 400 })
    }

    // Validate file type
    let allowedTypes: string[] = []
    let maxSize: number = 0

    switch (type) {
      case 'product':
        allowedTypes = ALLOWED_FILE_TYPES.PRODUCT_FILES
        maxSize = MAX_FILE_SIZES.PRODUCT_FILE
        break
      case 'image':
        allowedTypes = ALLOWED_FILE_TYPES.IMAGES
        maxSize = MAX_FILE_SIZES.IMAGE
        break
      case 'avatar':
        allowedTypes = ALLOWED_FILE_TYPES.IMAGES
        maxSize = MAX_FILE_SIZES.IMAGE
        break
    }

    if (!isValidFileType(file.name, allowedTypes)) {
      return NextResponse.json({
        success: false,
        message: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`
      }, { status: 400 })
    }

    // Validate file size
    if (file.size > maxSize) {
      return NextResponse.json({
        success: false,
        message: `File too large. Maximum size: ${Math.round(maxSize / 1024 / 1024)}MB`
      }, { status: 400 })
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())
    
    // Generate unique file key
    const fileKey = generateFileKey(type as any, file.name, user.id)
    
    // Upload to R2
    const uploadResult = await uploadFile(
      buffer,
      fileKey,
      file.type,
      {
        originalName: file.name,
        uploadedBy: user.id,
        uploadedAt: new Date().toISOString()
      }
    )

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        key: uploadResult.key,
        url: uploadResult.url,
        size: uploadResult.size,
        originalName: file.name,
        type: file.type
      }
    })

  } catch (error: any) {
    console.error('Error uploading file:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to upload file'
    }, { status: 500 })
  }
}

// GET /api/upload/signed-url - Generate signed URL for secure downloads
export async function GET(request: NextRequest) {
  try {
    const user = await requireAuth()
    const { searchParams } = new URL(request.url)
    const key = searchParams.get('key')
    const expiresIn = parseInt(searchParams.get('expiresIn') || '3600')

    if (!key) {
      return NextResponse.json({
        success: false,
        message: 'File key is required'
      }, { status: 400 })
    }

    // TODO: Add authorization check - ensure user has access to this file
    // This could involve checking if they purchased the product, etc.

    const signedUrl = await generateSignedUrl(key, expiresIn)

    return NextResponse.json({
      success: true,
      data: {
        url: signedUrl,
        expiresIn
      }
    })

  } catch (error: any) {
    console.error('Error generating signed URL:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to generate download URL'
    }, { status: 500 })
  }
}
