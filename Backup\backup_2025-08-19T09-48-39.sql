--
-- PostgreSQL database dump
--

-- Dumped from database version 17.2
-- Dumped by pg_dump version 17.5

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

DROP PUBLICATION IF EXISTS all_models;
ALTER TABLE IF EXISTS ONLY public.wishlist_items DROP CONSTRAINT IF EXISTS "wishlist_items_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.wishlist_items DROP CONSTRAINT IF EXISTS "wishlist_items_productId_fkey";
ALTER TABLE IF EXISTS ONLY public.sessions DROP CONSTRAINT IF EXISTS "sessions_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.reviews DROP CONSTRAINT IF EXISTS "reviews_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.reviews DROP CONSTRAINT IF EXISTS "reviews_productId_fkey";
ALTER TABLE IF EXISTS ONLY public.purchases DROP CONSTRAINT IF EXISTS "purchases_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.purchases DROP CONSTRAINT IF EXISTS "purchases_productId_fkey";
ALTER TABLE IF EXISTS ONLY public.purchases DROP CONSTRAINT IF EXISTS "purchases_orderId_fkey";
ALTER TABLE IF EXISTS ONLY public.products DROP CONSTRAINT IF EXISTS "products_categoryId_fkey";
ALTER TABLE IF EXISTS ONLY public.product_files DROP CONSTRAINT IF EXISTS "product_files_productId_fkey";
ALTER TABLE IF EXISTS ONLY public.payments DROP CONSTRAINT IF EXISTS "payments_orderId_fkey";
ALTER TABLE IF EXISTS ONLY public.orders DROP CONSTRAINT IF EXISTS "orders_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.orders DROP CONSTRAINT IF EXISTS "orders_couponId_fkey";
ALTER TABLE IF EXISTS ONLY public.order_items DROP CONSTRAINT IF EXISTS "order_items_productId_fkey";
ALTER TABLE IF EXISTS ONLY public.order_items DROP CONSTRAINT IF EXISTS "order_items_orderId_fkey";
ALTER TABLE IF EXISTS ONLY public.memberships DROP CONSTRAINT IF EXISTS "memberships_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.email_logs DROP CONSTRAINT IF EXISTS "email_logs_templateId_fkey";
ALTER TABLE IF EXISTS ONLY public.downloads DROP CONSTRAINT IF EXISTS "downloads_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.downloads DROP CONSTRAINT IF EXISTS "downloads_productId_fkey";
ALTER TABLE IF EXISTS ONLY public.download_tokens DROP CONSTRAINT IF EXISTS "download_tokens_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.download_tokens DROP CONSTRAINT IF EXISTS "download_tokens_productId_fkey";
ALTER TABLE IF EXISTS ONLY public.download_logs DROP CONSTRAINT IF EXISTS "download_logs_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.download_logs DROP CONSTRAINT IF EXISTS "download_logs_tokenId_fkey";
ALTER TABLE IF EXISTS ONLY public.download_logs DROP CONSTRAINT IF EXISTS "download_logs_productId_fkey";
ALTER TABLE IF EXISTS ONLY public.coupon_usages DROP CONSTRAINT IF EXISTS "coupon_usages_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.coupon_usages DROP CONSTRAINT IF EXISTS "coupon_usages_orderId_fkey";
ALTER TABLE IF EXISTS ONLY public.coupon_usages DROP CONSTRAINT IF EXISTS "coupon_usages_couponId_fkey";
ALTER TABLE IF EXISTS ONLY public.cart_items DROP CONSTRAINT IF EXISTS "cart_items_userId_fkey";
ALTER TABLE IF EXISTS ONLY public.cart_items DROP CONSTRAINT IF EXISTS "cart_items_productId_fkey";
ALTER TABLE IF EXISTS ONLY public.accounts DROP CONSTRAINT IF EXISTS "accounts_userId_fkey";
DROP INDEX IF EXISTS public."wishlist_items_userId_productId_key";
DROP INDEX IF EXISTS public.verification_tokens_token_key;
DROP INDEX IF EXISTS public.verification_tokens_identifier_token_key;
DROP INDEX IF EXISTS public.users_username_key;
DROP INDEX IF EXISTS public.users_email_key;
DROP INDEX IF EXISTS public."sessions_sessionToken_key";
DROP INDEX IF EXISTS public."reviews_userId_productId_key";
DROP INDEX IF EXISTS public."purchases_userId_productId_key";
DROP INDEX IF EXISTS public."purchases_userId_idx";
DROP INDEX IF EXISTS public."purchases_productId_idx";
DROP INDEX IF EXISTS public.products_slug_key;
DROP INDEX IF EXISTS public."orders_orderNumber_key";
DROP INDEX IF EXISTS public."memberships_userId_key";
DROP INDEX IF EXISTS public."download_tokens_userId_productId_idx";
DROP INDEX IF EXISTS public.download_tokens_token_key;
DROP INDEX IF EXISTS public.download_tokens_token_idx;
DROP INDEX IF EXISTS public."download_tokens_expiresAt_idx";
DROP INDEX IF EXISTS public."download_logs_userId_idx";
DROP INDEX IF EXISTS public."download_logs_productId_idx";
DROP INDEX IF EXISTS public."download_logs_downloadedAt_idx";
DROP INDEX IF EXISTS public.coupons_code_key;
DROP INDEX IF EXISTS public."coupon_usages_couponId_orderId_key";
DROP INDEX IF EXISTS public.categories_slug_key;
DROP INDEX IF EXISTS public.categories_name_key;
DROP INDEX IF EXISTS public."cart_items_userId_productId_key";
DROP INDEX IF EXISTS public."accounts_provider_providerAccountId_key";
ALTER TABLE IF EXISTS ONLY public.wishlist_items DROP CONSTRAINT IF EXISTS wishlist_items_pkey;
ALTER TABLE IF EXISTS ONLY public.users DROP CONSTRAINT IF EXISTS users_pkey;
ALTER TABLE IF EXISTS ONLY public.sessions DROP CONSTRAINT IF EXISTS sessions_pkey;
ALTER TABLE IF EXISTS ONLY public.reviews DROP CONSTRAINT IF EXISTS reviews_pkey;
ALTER TABLE IF EXISTS ONLY public.purchases DROP CONSTRAINT IF EXISTS purchases_pkey;
ALTER TABLE IF EXISTS ONLY public.products DROP CONSTRAINT IF EXISTS products_pkey;
ALTER TABLE IF EXISTS ONLY public.product_files DROP CONSTRAINT IF EXISTS product_files_pkey;
ALTER TABLE IF EXISTS ONLY public.payments DROP CONSTRAINT IF EXISTS payments_pkey;
ALTER TABLE IF EXISTS ONLY public.orders DROP CONSTRAINT IF EXISTS orders_pkey;
ALTER TABLE IF EXISTS ONLY public.order_items DROP CONSTRAINT IF EXISTS order_items_pkey;
ALTER TABLE IF EXISTS ONLY public.memberships DROP CONSTRAINT IF EXISTS memberships_pkey;
ALTER TABLE IF EXISTS ONLY public.email_templates DROP CONSTRAINT IF EXISTS email_templates_pkey;
ALTER TABLE IF EXISTS ONLY public.email_logs DROP CONSTRAINT IF EXISTS email_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.downloads DROP CONSTRAINT IF EXISTS downloads_pkey;
ALTER TABLE IF EXISTS ONLY public.download_tokens DROP CONSTRAINT IF EXISTS download_tokens_pkey;
ALTER TABLE IF EXISTS ONLY public.download_logs DROP CONSTRAINT IF EXISTS download_logs_pkey;
ALTER TABLE IF EXISTS ONLY public.coupons DROP CONSTRAINT IF EXISTS coupons_pkey;
ALTER TABLE IF EXISTS ONLY public.coupon_usages DROP CONSTRAINT IF EXISTS coupon_usages_pkey;
ALTER TABLE IF EXISTS ONLY public.categories DROP CONSTRAINT IF EXISTS categories_pkey;
ALTER TABLE IF EXISTS ONLY public.cart_items DROP CONSTRAINT IF EXISTS cart_items_pkey;
ALTER TABLE IF EXISTS ONLY public.accounts DROP CONSTRAINT IF EXISTS accounts_pkey;
ALTER TABLE IF EXISTS ONLY public._prisma_migrations DROP CONSTRAINT IF EXISTS _prisma_migrations_pkey;
DROP TABLE IF EXISTS public.wishlist_items;
DROP TABLE IF EXISTS public.verification_tokens;
DROP TABLE IF EXISTS public.users;
DROP TABLE IF EXISTS public.sessions;
DROP TABLE IF EXISTS public.reviews;
DROP TABLE IF EXISTS public.purchases;
DROP TABLE IF EXISTS public.products;
DROP TABLE IF EXISTS public.product_files;
DROP TABLE IF EXISTS public.payments;
DROP TABLE IF EXISTS public.orders;
DROP TABLE IF EXISTS public.order_items;
DROP TABLE IF EXISTS public.memberships;
DROP TABLE IF EXISTS public.email_templates;
DROP TABLE IF EXISTS public.email_logs;
DROP TABLE IF EXISTS public.downloads;
DROP TABLE IF EXISTS public.download_tokens;
DROP TABLE IF EXISTS public.download_logs;
DROP TABLE IF EXISTS public.coupons;
DROP TABLE IF EXISTS public.coupon_usages;
DROP TABLE IF EXISTS public.categories;
DROP TABLE IF EXISTS public.cart_items;
DROP TABLE IF EXISTS public.accounts;
DROP TABLE IF EXISTS public._prisma_migrations;
DROP TYPE IF EXISTS public."UserRole";
DROP TYPE IF EXISTS public."PurchaseStatus";
DROP TYPE IF EXISTS public."ProductStatus";
DROP TYPE IF EXISTS public."PaymentStatus";
DROP TYPE IF EXISTS public."OrderStatus";
DROP TYPE IF EXISTS public."MembershipType";
DROP TYPE IF EXISTS public."MembershipStatus";
DROP TYPE IF EXISTS public."EmailTemplateType";
DROP TYPE IF EXISTS public."EmailStatus";
DROP TYPE IF EXISTS public."CouponType";
-- *not* dropping schema, since initdb creates it
--
-- Name: public; Type: SCHEMA; Schema: -; Owner: prisma_migration
--

-- *not* creating schema, since initdb creates it


ALTER SCHEMA public OWNER TO prisma_migration;

--
-- Name: SCHEMA public; Type: COMMENT; Schema: -; Owner: prisma_migration
--

COMMENT ON SCHEMA public IS '';


--
-- Name: CouponType; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."CouponType" AS ENUM (
    'FIXED_AMOUNT',
    'PERCENTAGE',
    'FREE_SHIPPING'
);


ALTER TYPE public."CouponType" OWNER TO prisma_migration;

--
-- Name: EmailStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."EmailStatus" AS ENUM (
    'PENDING',
    'SENT',
    'DELIVERED',
    'OPENED',
    'CLICKED',
    'BOUNCED',
    'FAILED'
);


ALTER TYPE public."EmailStatus" OWNER TO prisma_migration;

--
-- Name: EmailTemplateType; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."EmailTemplateType" AS ENUM (
    'WELCOME',
    'ORDER_CONFIRMATION',
    'PASSWORD_RESET',
    'CUSTOM'
);


ALTER TYPE public."EmailTemplateType" OWNER TO prisma_migration;

--
-- Name: MembershipStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."MembershipStatus" AS ENUM (
    'ACTIVE',
    'EXPIRED',
    'CANCELLED'
);


ALTER TYPE public."MembershipStatus" OWNER TO prisma_migration;

--
-- Name: MembershipType; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."MembershipType" AS ENUM (
    'BRONZE',
    'SILVER',
    'GOLD'
);


ALTER TYPE public."MembershipType" OWNER TO prisma_migration;

--
-- Name: OrderStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."OrderStatus" AS ENUM (
    'PENDING',
    'PROCESSING',
    'COMPLETED',
    'CANCELLED',
    'REFUNDED'
);


ALTER TYPE public."OrderStatus" OWNER TO prisma_migration;

--
-- Name: PaymentStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."PaymentStatus" AS ENUM (
    'PENDING',
    'COMPLETED',
    'FAILED',
    'CANCELLED',
    'REFUNDED'
);


ALTER TYPE public."PaymentStatus" OWNER TO prisma_migration;

--
-- Name: ProductStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."ProductStatus" AS ENUM (
    'DRAFT',
    'PUBLISHED',
    'ARCHIVED'
);


ALTER TYPE public."ProductStatus" OWNER TO prisma_migration;

--
-- Name: PurchaseStatus; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."PurchaseStatus" AS ENUM (
    'PENDING',
    'COMPLETED',
    'CANCELLED',
    'REFUNDED'
);


ALTER TYPE public."PurchaseStatus" OWNER TO prisma_migration;

--
-- Name: UserRole; Type: TYPE; Schema: public; Owner: prisma_migration
--

CREATE TYPE public."UserRole" AS ENUM (
    'ADMIN',
    'CUSTOMER'
);


ALTER TYPE public."UserRole" OWNER TO prisma_migration;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO prisma_migration;

--
-- Name: accounts; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.accounts (
    id text NOT NULL,
    "userId" text NOT NULL,
    type text NOT NULL,
    provider text NOT NULL,
    "providerAccountId" text NOT NULL,
    refresh_token text,
    access_token text,
    expires_at integer,
    token_type text,
    scope text,
    id_token text,
    session_state text
);


ALTER TABLE public.accounts OWNER TO prisma_migration;

--
-- Name: cart_items; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.cart_items (
    id text NOT NULL,
    "userId" text NOT NULL,
    "productId" text NOT NULL,
    quantity integer DEFAULT 1 NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.cart_items OWNER TO prisma_migration;

--
-- Name: categories; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.categories (
    id text NOT NULL,
    name text NOT NULL,
    slug text NOT NULL,
    description text,
    image text,
    "isActive" boolean DEFAULT true NOT NULL,
    "sortOrder" integer DEFAULT 0 NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.categories OWNER TO prisma_migration;

--
-- Name: coupon_usages; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.coupon_usages (
    id text NOT NULL,
    "couponId" text NOT NULL,
    "userId" text,
    "orderId" text NOT NULL,
    "usedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.coupon_usages OWNER TO prisma_migration;

--
-- Name: coupons; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.coupons (
    id text NOT NULL,
    code text NOT NULL,
    name text NOT NULL,
    description text,
    type public."CouponType" NOT NULL,
    value numeric(10,2) NOT NULL,
    "minimumAmount" numeric(10,2),
    "maximumDiscount" numeric(10,2),
    "usageLimit" integer,
    "usageCount" integer DEFAULT 0 NOT NULL,
    "userUsageLimit" integer,
    "isActive" boolean DEFAULT true NOT NULL,
    "startsAt" timestamp(3) without time zone,
    "expiresAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.coupons OWNER TO prisma_migration;

--
-- Name: download_logs; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.download_logs (
    id text NOT NULL,
    "tokenId" text NOT NULL,
    "userId" text NOT NULL,
    "productId" text NOT NULL,
    "ipAddress" text NOT NULL,
    "userAgent" text NOT NULL,
    "downloadedAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.download_logs OWNER TO prisma_migration;

--
-- Name: download_tokens; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.download_tokens (
    id text NOT NULL,
    token text NOT NULL,
    "userId" text NOT NULL,
    "productId" text NOT NULL,
    "expiresAt" timestamp(3) without time zone NOT NULL,
    "downloadCount" integer DEFAULT 0 NOT NULL,
    "maxDownloads" integer DEFAULT 5 NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "lastDownloadAt" timestamp(3) without time zone,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.download_tokens OWNER TO prisma_migration;

--
-- Name: downloads; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.downloads (
    id text NOT NULL,
    "userId" text NOT NULL,
    "productId" text NOT NULL,
    "downloadUrl" text NOT NULL,
    "expiresAt" timestamp(3) without time zone,
    "downloadCount" integer DEFAULT 0 NOT NULL,
    "maxDownloads" integer,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.downloads OWNER TO prisma_migration;

--
-- Name: email_logs; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.email_logs (
    id text NOT NULL,
    "templateId" text,
    "to" text NOT NULL,
    "from" text NOT NULL,
    subject text NOT NULL,
    "htmlContent" text,
    "textContent" text,
    status public."EmailStatus" DEFAULT 'PENDING'::public."EmailStatus" NOT NULL,
    "sentAt" timestamp(3) without time zone,
    "deliveredAt" timestamp(3) without time zone,
    "openedAt" timestamp(3) without time zone,
    "clickedAt" timestamp(3) without time zone,
    "bouncedAt" timestamp(3) without time zone,
    "failedAt" timestamp(3) without time zone,
    "errorMessage" text,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.email_logs OWNER TO prisma_migration;

--
-- Name: email_templates; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.email_templates (
    id text NOT NULL,
    name text NOT NULL,
    subject text NOT NULL,
    "htmlContent" text NOT NULL,
    "textContent" text,
    type public."EmailTemplateType" NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    variables jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.email_templates OWNER TO prisma_migration;

--
-- Name: memberships; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.memberships (
    id text NOT NULL,
    "userId" text NOT NULL,
    type public."MembershipType" NOT NULL,
    status public."MembershipStatus" DEFAULT 'ACTIVE'::public."MembershipStatus" NOT NULL,
    "startDate" timestamp(3) without time zone NOT NULL,
    "endDate" timestamp(3) without time zone,
    "autoRenew" boolean DEFAULT false NOT NULL,
    "stripeCustomerId" text,
    "subscriptionId" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.memberships OWNER TO prisma_migration;

--
-- Name: order_items; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.order_items (
    id text NOT NULL,
    "orderId" text NOT NULL,
    "productId" text NOT NULL,
    quantity integer DEFAULT 1 NOT NULL,
    price numeric(10,2) NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.order_items OWNER TO prisma_migration;

--
-- Name: orders; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.orders (
    id text NOT NULL,
    "orderNumber" text NOT NULL,
    "userId" text,
    email text NOT NULL,
    "firstName" text NOT NULL,
    "lastName" text NOT NULL,
    status public."OrderStatus" DEFAULT 'PENDING'::public."OrderStatus" NOT NULL,
    "totalAmount" numeric(10,2) NOT NULL,
    "paymentStatus" public."PaymentStatus" DEFAULT 'PENDING'::public."PaymentStatus" NOT NULL,
    "paymentMethod" text,
    "paymentIntentId" text,
    notes text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "couponCode" text,
    "couponId" text,
    "discountAmount" numeric(10,2) DEFAULT 0 NOT NULL,
    "subtotalAmount" numeric(10,2) NOT NULL
);


ALTER TABLE public.orders OWNER TO prisma_migration;

--
-- Name: payments; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.payments (
    id text NOT NULL,
    "orderId" text NOT NULL,
    amount numeric(10,2) NOT NULL,
    currency text DEFAULT 'USD'::text NOT NULL,
    status public."PaymentStatus" DEFAULT 'PENDING'::public."PaymentStatus" NOT NULL,
    "paymentMethod" text NOT NULL,
    "transactionId" text,
    "paymentIntentId" text,
    metadata jsonb,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.payments OWNER TO prisma_migration;

--
-- Name: product_files; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.product_files (
    id text NOT NULL,
    "productId" text NOT NULL,
    "fileName" text NOT NULL,
    "fileUrl" text NOT NULL,
    "fileSize" bigint NOT NULL,
    "fileType" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.product_files OWNER TO prisma_migration;

--
-- Name: products; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.products (
    id text NOT NULL,
    name text NOT NULL,
    slug text NOT NULL,
    description text,
    "shortDescription" text,
    price numeric(10,2) NOT NULL,
    "originalPrice" numeric(10,2),
    "isOnSale" boolean DEFAULT false NOT NULL,
    "salePrice" numeric(10,2),
    images text[],
    "categoryId" text NOT NULL,
    status public."ProductStatus" DEFAULT 'DRAFT'::public."ProductStatus" NOT NULL,
    featured boolean DEFAULT false NOT NULL,
    "downloadLimit" integer,
    "downloadExpiry" integer,
    tags text[],
    "metaTitle" text,
    "metaDescription" text,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL,
    "fileKey" text,
    "fileName" text,
    "fileSize" bigint
);


ALTER TABLE public.products OWNER TO prisma_migration;

--
-- Name: purchases; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.purchases (
    id text NOT NULL,
    "userId" text NOT NULL,
    "productId" text NOT NULL,
    "orderId" text,
    status public."PurchaseStatus" DEFAULT 'PENDING'::public."PurchaseStatus" NOT NULL,
    price numeric(10,2) NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.purchases OWNER TO prisma_migration;

--
-- Name: reviews; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.reviews (
    id text NOT NULL,
    "userId" text NOT NULL,
    "productId" text NOT NULL,
    rating smallint NOT NULL,
    title text,
    comment text,
    "isVerified" boolean DEFAULT false NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.reviews OWNER TO prisma_migration;

--
-- Name: sessions; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.sessions (
    id text NOT NULL,
    "sessionToken" text NOT NULL,
    "userId" text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.sessions OWNER TO prisma_migration;

--
-- Name: users; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.users (
    id text NOT NULL,
    email text NOT NULL,
    username text,
    "firstName" text,
    "lastName" text,
    avatar text,
    "emailVerified" timestamp(3) without time zone,
    password text,
    role public."UserRole" DEFAULT 'CUSTOMER'::public."UserRole" NOT NULL,
    "isActive" boolean DEFAULT true NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updatedAt" timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.users OWNER TO prisma_migration;

--
-- Name: verification_tokens; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.verification_tokens (
    identifier text NOT NULL,
    token text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.verification_tokens OWNER TO prisma_migration;

--
-- Name: wishlist_items; Type: TABLE; Schema: public; Owner: prisma_migration
--

CREATE TABLE public.wishlist_items (
    id text NOT NULL,
    "userId" text NOT NULL,
    "productId" text NOT NULL,
    "createdAt" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.wishlist_items OWNER TO prisma_migration;

--
-- Data for Name: _prisma_migrations; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public._prisma_migrations (id, checksum, finished_at, migration_name, logs, rolled_back_at, started_at, applied_steps_count) FROM stdin;
c166e788-054e-489f-afd2-5e9950ec4782	1a8a6efa604fb57ead3d4246ff6070126133ade698db0afc7cbf6232e5a0597a	2025-08-18 19:13:55.997489+00	20250818110003_init	\N	\N	2025-08-18 19:13:55.701509+00	1
\.


--
-- Data for Name: accounts; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.accounts (id, "userId", type, provider, "providerAccountId", refresh_token, access_token, expires_at, token_type, scope, id_token, session_state) FROM stdin;
\.


--
-- Data for Name: cart_items; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.cart_items (id, "userId", "productId", quantity, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: categories; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.categories (id, name, slug, description, image, "isActive", "sortOrder", "createdAt", "updatedAt") FROM stdin;
cmehhtxku0001c69ycst9nmip	MT4 EA	mt4-ea	Premium Expert Advisors for MetaTrader 4	/images/categories/mt4-ea.jpg	t	1	2025-08-18 19:13:57.823	2025-08-18 19:13:57.823
cmehhtxu20002c69ycsdlzep2	MT5 EA	mt5-ea	Premium Expert Advisors for MetaTrader 5	/images/categories/mt5-ea.jpg	t	2	2025-08-18 19:13:58.154	2025-08-18 19:13:58.154
cmehhtxzt0003c69y09fro020	MT4 Indicators	mt4-indicators	Professional technical indicators for MetaTrader 4	/images/categories/mt4-indicators.jpg	t	3	2025-08-18 19:13:58.361	2025-08-18 19:13:58.361
cmehhty5h0004c69ymmgyupn7	MT5 Indicators	mt5-indicators	Professional technical indicators for MetaTrader 5	/images/categories/mt5-indicators.jpg	t	4	2025-08-18 19:13:58.565	2025-08-18 19:13:58.565
cmehhtyb40005c69ynlsz9mj2	Trading Systems	trading-systems	Complete trading systems and strategies	/images/categories/trading-systems.jpg	t	5	2025-08-18 19:13:58.768	2025-08-18 19:13:58.768
\.


--
-- Data for Name: coupon_usages; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.coupon_usages (id, "couponId", "userId", "orderId", "usedAt") FROM stdin;
\.


--
-- Data for Name: coupons; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.coupons (id, code, name, description, type, value, "minimumAmount", "maximumDiscount", "usageLimit", "usageCount", "userUsageLimit", "isActive", "startsAt", "expiresAt", "createdAt", "updatedAt") FROM stdin;
cmehi66i10000nu9rfvv4lfmv	WELCOME10	Welcome 10% Off	Welcome discount for new customers	PERCENTAGE	10.00	50.00	\N	100	0	1	t	\N	2025-09-17 19:23:28.892	2025-08-18 19:23:29.258	2025-08-18 19:23:29.258
cmehi66kp0001nu9rxf6aadhr	SAVE25	$25 Off Orders Over $100	Fixed amount discount for larger orders	FIXED_AMOUNT	25.00	100.00	\N	50	0	2	t	\N	2025-10-17 19:23:28.892	2025-08-18 19:23:29.353	2025-08-18 19:23:29.353
cmehi66lw0002nu9r4m9rzaiy	BLACKFRIDAY	Black Friday 30% Off	Black Friday special discount	PERCENTAGE	30.00	\N	100.00	200	0	1	t	2024-11-29 00:00:00	2024-12-02 00:00:00	2025-08-18 19:23:29.396	2025-08-18 19:23:29.396
cmehi66o80003nu9rj22kwa8x	UNLIMITED20	20% Off - No Limits	Unlimited usage percentage discount	PERCENTAGE	20.00	\N	\N	\N	0	\N	t	\N	\N	2025-08-18 19:23:29.481	2025-08-18 19:23:29.481
cmehi66qk0004nu9rl1lbn0f8	EXPIRED	Expired Test Coupon	Test coupon that has expired	PERCENTAGE	50.00	\N	\N	\N	0	\N	t	\N	2025-08-17 19:23:28.892	2025-08-18 19:23:29.565	2025-08-18 19:23:29.565
\.


--
-- Data for Name: download_logs; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.download_logs (id, "tokenId", "userId", "productId", "ipAddress", "userAgent", "downloadedAt") FROM stdin;
\.


--
-- Data for Name: download_tokens; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.download_tokens (id, token, "userId", "productId", "expiresAt", "downloadCount", "maxDownloads", "isActive", "lastDownloadAt", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: downloads; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.downloads (id, "userId", "productId", "downloadUrl", "expiresAt", "downloadCount", "maxDownloads", "createdAt") FROM stdin;
\.


--
-- Data for Name: email_logs; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.email_logs (id, "templateId", "to", "from", subject, "htmlContent", "textContent", status, "sentAt", "deliveredAt", "openedAt", "clickedAt", "bouncedAt", "failedAt", "errorMessage", metadata, "createdAt", "updatedAt") FROM stdin;
cmeibo83c0000n6mt18yfn8lh	\N	<EMAIL>	Forex Bot Zone <<EMAIL>>	[TEST] Order Confirmation - Your Purchase is Complete!	\n        <div style="background: #f59e0b; color: white; padding: 10px; text-align: center; border-radius: 5px; margin-bottom: 20px;">\n          <strong>🧪 TEST EMAIL - Order Confirmation</strong>\n        </div>\n        \n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset="utf-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>Order Confirmation - Forex Bot Zone</title>\n        </head>\n        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">\n          <div style="background: linear-gradient(135deg, #1f2937 0%, #10b981 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">\n            <h1 style="color: #fff; margin: 0; font-size: 28px;">Order Confirmed!</h1>\n          </div>\n          \n          <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">\n            <h2 style="color: #1f2937; margin-top: 0;">Thank you, sangeeth!</h2>\n            \n            <p>Your order has been successfully processed and is ready for download.</p>\n            \n            <div style="background: #fff; border: 1px solid #e5e7eb; padding: 20px; border-radius: 8px; margin: 20px 0;">\n              <h3 style="color: #1f2937; margin-top: 0;">Order Details</h3>\n              <p><strong>Order Number:</strong> TEST-12345</p>\n              <p><strong>Total:</strong> $99.99</p>\n            </div>\n            \n            <div style="text-align: center; margin: 30px 0;">\n              <a href="http://localhost:3000/downloads/test" style="background: #10b981; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">\n                Download Your Files\n              </a>\n            </div>\n            \n            <p style="color: #6b7280; font-size: 14px;">\n              Your download link will remain active for the duration specified in your purchase terms.\n            </p>\n            \n            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">\n            \n            <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">\n              © 2024 Forex Bot Zone. All rights reserved.\n            </p>\n          </div>\n        </body>\n        </html>\n      \n        <div style="margin-top: 30px; padding: 15px; background: #f3f4f6; border-radius: 5px; border-left: 4px solid #f59e0b;">\n          <p style="margin: 0; color: #666; font-size: 12px;">\n            <strong>Test Information:</strong><br>\n            Template: Order Confirmation (ORDER_CONFIRMATION)<br>\n            Sent from: Forex Bot Zone Admin Panel<br>\n            Environment: development\n          </p>\n        </div>\n      	[TEST] Order Confirmation - Your Purchase is Complete!\n\n\n        Order Confirmation - Forex Bot Zone\n        \n        Thank you, sangeeth!\n        \n        Your order has been successfully processed and is ready for download.\n        \n        Order Details:\n        Order Number: TEST-12345\n        Total: $99.99\n        \n        Download your files: http://localhost:3000/downloads/test\n        \n        Your download link will remain active for the duration specified in your purchase terms.\n        \n        © 2024 Forex Bot Zone. All rights reserved.\n      \n\n---\nTest Information:\nTemplate: Order Confirmation (ORDER_CONFIRMATION)\nSent from: Forex Bot Zone Admin Panel\nEnvironment: development	SENT	2025-08-19 09:09:21.479	\N	\N	\N	\N	\N	\N	\N	2025-08-19 09:09:19.992	2025-08-19 09:09:21.48
cmeibo9b70002n6mtpc7cx7e5	order-confirmation-template	<EMAIL>	Forex Bot Zone <<EMAIL>>	[TEST] Order Confirmation - Your Purchase is Complete!	\N	\N	SENT	2025-08-19 09:09:21.57	\N	\N	\N	\N	\N	\N	{"isTest": true, "sentBy": "<EMAIL>", "variables": {"email": "<EMAIL>", "total": "99.99", "loginUrl": "http://localhost:3000/auth/signin", "resetUrl": "http://localhost:3000/auth/reset-password?token=test-token", "expiresIn": "24 hours", "firstName": "sangeeth", "downloadUrl": "http://localhost:3000/downloads/test", "orderNumber": "TEST-12345", "customerName": "sangeeth"}}	2025-08-19 09:09:21.572	2025-08-19 09:09:21.572
\.


--
-- Data for Name: email_templates; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.email_templates (id, name, subject, "htmlContent", "textContent", type, "isActive", variables, "createdAt", "updatedAt") FROM stdin;
welcome-template	Welcome Email	Welcome to Forex Bot Zone - Your Trading Journey Starts Here!	\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset="utf-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>Welcome to Forex Bot Zone</title>\n        </head>\n        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">\n          <div style="background: linear-gradient(135deg, #1f2937 0%, #3b82f6 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">\n            <h1 style="color: #fbbf24; margin: 0; font-size: 28px;">Welcome to Forex Bot Zone!</h1>\n          </div>\n          \n          <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">\n            <h2 style="color: #1f2937; margin-top: 0;">Hello {{firstName}}!</h2>\n            \n            <p>Thank you for joining <strong>Forex Bot Zone</strong>, your premier destination for professional forex trading tools and expert advisors.</p>\n            \n            <p>Your account has been successfully created with the email: <strong>{{email}}</strong></p>\n            \n            <div style="background: #e0f2fe; padding: 20px; border-radius: 8px; margin: 20px 0;">\n              <h3 style="color: #0277bd; margin-top: 0;">What's Next?</h3>\n              <ul style="color: #01579b; margin: 0; padding-left: 20px;">\n                <li>Browse our premium collection of Expert Advisors</li>\n                <li>Explore professional trading indicators</li>\n                <li>Access exclusive trading strategies</li>\n                <li>Join our community of successful traders</li>\n              </ul>\n            </div>\n            \n            <div style="text-align: center; margin: 30px 0;">\n              <a href="{{loginUrl}}" style="background: #fbbf24; color: #1f2937; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">\n                Access Your Account\n              </a>\n            </div>\n            \n            <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">\n              If you have any questions, feel free to contact our support team.\n            </p>\n            \n            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">\n            \n            <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">\n              © 2024 Forex Bot Zone. All rights reserved.\n            </p>\n          </div>\n        </body>\n        </html>\n      	\n        Welcome to Forex Bot Zone!\n        \n        Hello {{firstName}}!\n        \n        Thank you for joining Forex Bot Zone, your premier destination for professional forex trading tools and expert advisors.\n        \n        Your account has been successfully created with the email: {{email}}\n        \n        What's Next?\n        - Browse our premium collection of Expert Advisors\n        - Explore professional trading indicators\n        - Access exclusive trading strategies\n        - Join our community of successful traders\n        \n        Access your account: {{loginUrl}}\n        \n        If you have any questions, feel free to contact our support team.\n        \n        © 2024 Forex Bot Zone. All rights reserved.\n      	WELCOME	t	{"email": "User's email address", "loginUrl": "Login page URL", "firstName": "User's first name"}	2025-08-19 09:08:40.178	2025-08-19 09:08:40.178
password-reset-template	Password Reset	Reset Your Password - Forex Bot Zone	\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset="utf-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>Reset Your Password - Forex Bot Zone</title>\n        </head>\n        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">\n          <div style="background: linear-gradient(135deg, #1f2937 0%, #ef4444 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">\n            <h1 style="color: #fff; margin: 0; font-size: 28px;">Password Reset</h1>\n          </div>\n          \n          <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">\n            <h2 style="color: #1f2937; margin-top: 0;">Hello {{firstName}}!</h2>\n            \n            <p>We received a request to reset your password for your Forex Bot Zone account.</p>\n            \n            <div style="text-align: center; margin: 30px 0;">\n              <a href="{{resetUrl}}" style="background: #ef4444; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">\n                Reset Your Password\n              </a>\n            </div>\n            \n            <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 6px; margin: 20px 0;">\n              <p style="color: #dc2626; margin: 0; font-size: 14px;">\n                <strong>Important:</strong> This link will expire in {{expiresIn}}. If you didn't request this password reset, please ignore this email.\n              </p>\n            </div>\n            \n            <p style="color: #6b7280; font-size: 14px;">\n              For security reasons, this link can only be used once and will expire automatically.\n            </p>\n            \n            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">\n            \n            <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">\n              © 2024 Forex Bot Zone. All rights reserved.\n            </p>\n          </div>\n        </body>\n        </html>\n      	\n        Password Reset - Forex Bot Zone\n        \n        Hello {{firstName}}!\n        \n        We received a request to reset your password for your Forex Bot Zone account.\n        \n        Reset your password: {{resetUrl}}\n        \n        Important: This link will expire in {{expiresIn}}. If you didn't request this password reset, please ignore this email.\n        \n        For security reasons, this link can only be used once and will expire automatically.\n        \n        © 2024 Forex Bot Zone. All rights reserved.\n      	PASSWORD_RESET	t	{"resetUrl": "Password reset URL with token", "expiresIn": "Expiration time (e.g., 24 hours)", "firstName": "User's first name"}	2025-08-19 09:08:40.532	2025-08-19 09:08:40.532
order-confirmation-template	Order Confirmation	Order Confirmation - Your Purchase is Complete!	\n        <!DOCTYPE html>\n        <html>\n        <head>\n          <meta charset="utf-8">\n          <meta name="viewport" content="width=device-width, initial-scale=1.0">\n          <title>Order Confirmation - Forex Bot Zone</title>\n        </head>\n        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">\n          <div style="background: linear-gradient(135deg, #1f2937 0%, #10b981 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">\n            <h1 style="color: #fff; margin: 0; font-size: 28px;">Order Confirmed!</h1>\n          </div>\n          \n          <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">\n            <h2 style="color: #1f2937; margin-top: 0;">Thank you, {{customerName}}!</h2>\n            \n            <p>Your order has been successfully processed and is ready for download.</p>\n            \n            <div style="background: #fff; border: 1px solid #e5e7eb; padding: 20px; border-radius: 8px; margin: 20px 0;">\n              <h3 style="color: #1f2937; margin-top: 0;">Order Details</h3>\n              <p><strong>Order Number:</strong> {{orderNumber}}</p>\n              <p><strong>Total:</strong> ${{total}}</p>\n            </div>\n            \n            <div style="text-align: center; margin: 30px 0;">\n              <a href="{{downloadUrl}}" style="background: #10b981; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">\n                Download Your Files\n              </a>\n            </div>\n            \n            <p style="color: #6b7280; font-size: 14px;">\n              Your download link will remain active for the duration specified in your purchase terms.\n            </p>\n            \n            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">\n            \n            <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">\n              © 2024 Forex Bot Zone. All rights reserved.\n            </p>\n          </div>\n        </body>\n        </html>\n      	\n        Order Confirmation - Forex Bot Zone\n        \n        Thank you, {{customerName}}!\n        \n        Your order has been successfully processed and is ready for download.\n        \n        Order Details:\n        Order Number: {{orderNumber}}\n        Total: ${{total}}\n        \n        Download your files: {{downloadUrl}}\n        \n        Your download link will remain active for the duration specified in your purchase terms.\n        \n        © 2024 Forex Bot Zone. All rights reserved.\n      	ORDER_CONFIRMATION	t	{"total": "Order total amount", "downloadUrl": "Download page URL", "orderNumber": "Order number", "customerName": "Customer's name"}	2025-08-19 09:08:40.739	2025-08-19 09:08:40.739
\.


--
-- Data for Name: memberships; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.memberships (id, "userId", type, status, "startDate", "endDate", "autoRenew", "stripeCustomerId", "subscriptionId", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: order_items; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.order_items (id, "orderId", "productId", quantity, price, "createdAt") FROM stdin;
\.


--
-- Data for Name: orders; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.orders (id, "orderNumber", "userId", email, "firstName", "lastName", status, "totalAmount", "paymentStatus", "paymentMethod", "paymentIntentId", notes, "createdAt", "updatedAt", "couponCode", "couponId", "discountAmount", "subtotalAmount") FROM stdin;
\.


--
-- Data for Name: payments; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.payments (id, "orderId", amount, currency, status, "paymentMethod", "transactionId", "paymentIntentId", metadata, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: product_files; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.product_files (id, "productId", "fileName", "fileUrl", "fileSize", "fileType", "createdAt") FROM stdin;
\.


--
-- Data for Name: products; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.products (id, name, slug, description, "shortDescription", price, "originalPrice", "isOnSale", "salePrice", images, "categoryId", status, featured, "downloadLimit", "downloadExpiry", tags, "metaTitle", "metaDescription", "createdAt", "updatedAt", "fileKey", "fileName", "fileSize") FROM stdin;
cmeicmg1o0001hhh3cuuyi54h	Quantum Queen MT5	quantum-queen-mt5	Quantum Queen MT5 is an advanced, next-generation automated trading Expert Advisor (EA) developed for the MetaTrader 5 platform. Utilizing cutting-edge quantum-inspired signal processing, sophisticated trend modeling, and adaptive entry strategies, this EA aims to execute highly accurate trades while maintaining minimal drawdowns. It is optimized for performance across major currency pairs as well as Gold (XAUUSD).	Quantum Queen MT5 is an advanced, next-generation automated trading Expert Advisor (EA) developed for the MetaTrader 5 platform. 	49.00	1349.00	t	29.00	{/uploads/products/1755595950663-d470b7fa.jpg,/uploads/products/1755595950672-42052d77.png,/uploads/products/1755595950661-7a9544fa.png,/uploads/products/1755595950664-8f2b29fa.jpg}	cmehhtxu20002c69ycsdlzep2	PUBLISHED	t	10	365	{"Quantum Queen","MT5 EA"}	Quantum Queen MT5	Quantum Queen MT5 is an advanced, next-generation automated trading Expert Advisor (EA) developed for the MetaTrader 5 platform. Utilizing cutting-edge quantum-inspired signal processing, sophisticated trend modeling, and adaptive entry strategies, this EA aims to execute highly accurate trades while maintaining minimal drawdowns. It is optimized for performance across major currency pairs as well as Gold (XAUUSD).	2025-08-19 09:35:56.605	2025-08-19 09:35:56.605	https://botzone.b6e9d709f59807a7692e9ac22e76c0b8.r2.cloudflarestorage.com/products/1755596014525-l45cuda544-Quantum%20Queen%20v2.1.ex5?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Content-Sha256=UNSIGNED-PAYLOAD&X-Amz-Credential=dc5cf4200d9e1188ff2759dd0c0da732%2F20250819%2Fauto%2Fs3%2Faws4_request&X-Amz-Date=20250819T093336Z&X-Amz-Expires=604800&X-Amz-Signature=2b941e741d307e75e32de3e2914f70bbb2a9c01fd0c8f1234d43cba4c4b75c38&X-Amz-SignedHeaders=host&x-amz-checksum-mode=ENABLED&x-id=GetObject	Quantum Queen v2.1.ex5	378642
\.


--
-- Data for Name: purchases; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.purchases (id, "userId", "productId", "orderId", status, price, "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: reviews; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.reviews (id, "userId", "productId", rating, title, comment, "isVerified", "createdAt", "updatedAt") FROM stdin;
\.


--
-- Data for Name: sessions; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.sessions (id, "sessionToken", "userId", expires) FROM stdin;
\.


--
-- Data for Name: users; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.users (id, email, username, "firstName", "lastName", avatar, "emailVerified", password, role, "isActive", "createdAt", "updatedAt") FROM stdin;
cmehhtxic0000c69yg4vspmb6	<EMAIL>	admin	Sangeeth	Thilakarathna	\N	2025-08-18 19:19:02.614	$2a$12$QXSo0nNb.aje17pyvFeoNeApZypPhRKXKRKsHKDS04iDTQl8ISQSq	ADMIN	t	2025-08-18 19:13:57.732	2025-08-19 09:19:04.601
\.


--
-- Data for Name: verification_tokens; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.verification_tokens (identifier, token, expires) FROM stdin;
\.


--
-- Data for Name: wishlist_items; Type: TABLE DATA; Schema: public; Owner: prisma_migration
--

COPY public.wishlist_items (id, "userId", "productId", "createdAt") FROM stdin;
\.


--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- Name: cart_items cart_items_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.cart_items
    ADD CONSTRAINT cart_items_pkey PRIMARY KEY (id);


--
-- Name: categories categories_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.categories
    ADD CONSTRAINT categories_pkey PRIMARY KEY (id);


--
-- Name: coupon_usages coupon_usages_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.coupon_usages
    ADD CONSTRAINT coupon_usages_pkey PRIMARY KEY (id);


--
-- Name: coupons coupons_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.coupons
    ADD CONSTRAINT coupons_pkey PRIMARY KEY (id);


--
-- Name: download_logs download_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.download_logs
    ADD CONSTRAINT download_logs_pkey PRIMARY KEY (id);


--
-- Name: download_tokens download_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.download_tokens
    ADD CONSTRAINT download_tokens_pkey PRIMARY KEY (id);


--
-- Name: downloads downloads_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.downloads
    ADD CONSTRAINT downloads_pkey PRIMARY KEY (id);


--
-- Name: email_logs email_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.email_logs
    ADD CONSTRAINT email_logs_pkey PRIMARY KEY (id);


--
-- Name: email_templates email_templates_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.email_templates
    ADD CONSTRAINT email_templates_pkey PRIMARY KEY (id);


--
-- Name: memberships memberships_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.memberships
    ADD CONSTRAINT memberships_pkey PRIMARY KEY (id);


--
-- Name: order_items order_items_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT order_items_pkey PRIMARY KEY (id);


--
-- Name: orders orders_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT orders_pkey PRIMARY KEY (id);


--
-- Name: payments payments_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT payments_pkey PRIMARY KEY (id);


--
-- Name: product_files product_files_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.product_files
    ADD CONSTRAINT product_files_pkey PRIMARY KEY (id);


--
-- Name: products products_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT products_pkey PRIMARY KEY (id);


--
-- Name: purchases purchases_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.purchases
    ADD CONSTRAINT purchases_pkey PRIMARY KEY (id);


--
-- Name: reviews reviews_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT reviews_pkey PRIMARY KEY (id);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: wishlist_items wishlist_items_pkey; Type: CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.wishlist_items
    ADD CONSTRAINT wishlist_items_pkey PRIMARY KEY (id);


--
-- Name: accounts_provider_providerAccountId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "accounts_provider_providerAccountId_key" ON public.accounts USING btree (provider, "providerAccountId");


--
-- Name: cart_items_userId_productId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "cart_items_userId_productId_key" ON public.cart_items USING btree ("userId", "productId");


--
-- Name: categories_name_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX categories_name_key ON public.categories USING btree (name);


--
-- Name: categories_slug_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX categories_slug_key ON public.categories USING btree (slug);


--
-- Name: coupon_usages_couponId_orderId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "coupon_usages_couponId_orderId_key" ON public.coupon_usages USING btree ("couponId", "orderId");


--
-- Name: coupons_code_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX coupons_code_key ON public.coupons USING btree (code);


--
-- Name: download_logs_downloadedAt_idx; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE INDEX "download_logs_downloadedAt_idx" ON public.download_logs USING btree ("downloadedAt");


--
-- Name: download_logs_productId_idx; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE INDEX "download_logs_productId_idx" ON public.download_logs USING btree ("productId");


--
-- Name: download_logs_userId_idx; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE INDEX "download_logs_userId_idx" ON public.download_logs USING btree ("userId");


--
-- Name: download_tokens_expiresAt_idx; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE INDEX "download_tokens_expiresAt_idx" ON public.download_tokens USING btree ("expiresAt");


--
-- Name: download_tokens_token_idx; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE INDEX download_tokens_token_idx ON public.download_tokens USING btree (token);


--
-- Name: download_tokens_token_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX download_tokens_token_key ON public.download_tokens USING btree (token);


--
-- Name: download_tokens_userId_productId_idx; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE INDEX "download_tokens_userId_productId_idx" ON public.download_tokens USING btree ("userId", "productId");


--
-- Name: memberships_userId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "memberships_userId_key" ON public.memberships USING btree ("userId");


--
-- Name: orders_orderNumber_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "orders_orderNumber_key" ON public.orders USING btree ("orderNumber");


--
-- Name: products_slug_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX products_slug_key ON public.products USING btree (slug);


--
-- Name: purchases_productId_idx; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE INDEX "purchases_productId_idx" ON public.purchases USING btree ("productId");


--
-- Name: purchases_userId_idx; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE INDEX "purchases_userId_idx" ON public.purchases USING btree ("userId");


--
-- Name: purchases_userId_productId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "purchases_userId_productId_key" ON public.purchases USING btree ("userId", "productId");


--
-- Name: reviews_userId_productId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "reviews_userId_productId_key" ON public.reviews USING btree ("userId", "productId");


--
-- Name: sessions_sessionToken_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "sessions_sessionToken_key" ON public.sessions USING btree ("sessionToken");


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: users_username_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX users_username_key ON public.users USING btree (username);


--
-- Name: verification_tokens_identifier_token_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX verification_tokens_identifier_token_key ON public.verification_tokens USING btree (identifier, token);


--
-- Name: verification_tokens_token_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX verification_tokens_token_key ON public.verification_tokens USING btree (token);


--
-- Name: wishlist_items_userId_productId_key; Type: INDEX; Schema: public; Owner: prisma_migration
--

CREATE UNIQUE INDEX "wishlist_items_userId_productId_key" ON public.wishlist_items USING btree ("userId", "productId");


--
-- Name: accounts accounts_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT "accounts_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: cart_items cart_items_productId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.cart_items
    ADD CONSTRAINT "cart_items_productId_fkey" FOREIGN KEY ("productId") REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: cart_items cart_items_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.cart_items
    ADD CONSTRAINT "cart_items_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: coupon_usages coupon_usages_couponId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.coupon_usages
    ADD CONSTRAINT "coupon_usages_couponId_fkey" FOREIGN KEY ("couponId") REFERENCES public.coupons(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: coupon_usages coupon_usages_orderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.coupon_usages
    ADD CONSTRAINT "coupon_usages_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES public.orders(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: coupon_usages coupon_usages_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.coupon_usages
    ADD CONSTRAINT "coupon_usages_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: download_logs download_logs_productId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.download_logs
    ADD CONSTRAINT "download_logs_productId_fkey" FOREIGN KEY ("productId") REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: download_logs download_logs_tokenId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.download_logs
    ADD CONSTRAINT "download_logs_tokenId_fkey" FOREIGN KEY ("tokenId") REFERENCES public.download_tokens(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: download_logs download_logs_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.download_logs
    ADD CONSTRAINT "download_logs_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: download_tokens download_tokens_productId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.download_tokens
    ADD CONSTRAINT "download_tokens_productId_fkey" FOREIGN KEY ("productId") REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: download_tokens download_tokens_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.download_tokens
    ADD CONSTRAINT "download_tokens_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: downloads downloads_productId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.downloads
    ADD CONSTRAINT "downloads_productId_fkey" FOREIGN KEY ("productId") REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: downloads downloads_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.downloads
    ADD CONSTRAINT "downloads_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: email_logs email_logs_templateId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.email_logs
    ADD CONSTRAINT "email_logs_templateId_fkey" FOREIGN KEY ("templateId") REFERENCES public.email_templates(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: memberships memberships_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.memberships
    ADD CONSTRAINT "memberships_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: order_items order_items_orderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT "order_items_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES public.orders(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: order_items order_items_productId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.order_items
    ADD CONSTRAINT "order_items_productId_fkey" FOREIGN KEY ("productId") REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: orders orders_couponId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT "orders_couponId_fkey" FOREIGN KEY ("couponId") REFERENCES public.coupons(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: orders orders_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.orders
    ADD CONSTRAINT "orders_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: payments payments_orderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.payments
    ADD CONSTRAINT "payments_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES public.orders(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: product_files product_files_productId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.product_files
    ADD CONSTRAINT "product_files_productId_fkey" FOREIGN KEY ("productId") REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: products products_categoryId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.products
    ADD CONSTRAINT "products_categoryId_fkey" FOREIGN KEY ("categoryId") REFERENCES public.categories(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: purchases purchases_orderId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.purchases
    ADD CONSTRAINT "purchases_orderId_fkey" FOREIGN KEY ("orderId") REFERENCES public.orders(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: purchases purchases_productId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.purchases
    ADD CONSTRAINT "purchases_productId_fkey" FOREIGN KEY ("productId") REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: purchases purchases_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.purchases
    ADD CONSTRAINT "purchases_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: reviews reviews_productId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT "reviews_productId_fkey" FOREIGN KEY ("productId") REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: reviews reviews_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.reviews
    ADD CONSTRAINT "reviews_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE RESTRICT;


--
-- Name: sessions sessions_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT "sessions_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: wishlist_items wishlist_items_productId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.wishlist_items
    ADD CONSTRAINT "wishlist_items_productId_fkey" FOREIGN KEY ("productId") REFERENCES public.products(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: wishlist_items wishlist_items_userId_fkey; Type: FK CONSTRAINT; Schema: public; Owner: prisma_migration
--

ALTER TABLE ONLY public.wishlist_items
    ADD CONSTRAINT "wishlist_items_userId_fkey" FOREIGN KEY ("userId") REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: all_models; Type: PUBLICATION; Schema: -; Owner: postgres
--

CREATE PUBLICATION all_models FOR ALL TABLES WITH (publish = 'insert, update, delete, truncate');


ALTER PUBLICATION all_models OWNER TO postgres;

--
-- PostgreSQL database dump complete
--

