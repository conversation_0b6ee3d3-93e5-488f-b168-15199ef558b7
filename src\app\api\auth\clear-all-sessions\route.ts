import { NextRequest, NextResponse } from 'next/server'
import { cookies } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const cookieStore = cookies()
    const response = NextResponse.json({ 
      success: true, 
      message: 'All sessions and cookies cleared successfully' 
    })
    
    // Get all cookies and clear them
    const allCookies = cookieStore.getAll()
    
    // Clear all cookies, not just NextAuth ones
    allCookies.forEach(cookie => {
      response.cookies.delete(cookie.name)
      // Also try to delete with different path and domain combinations
      response.cookies.delete(cookie.name, { path: '/' })
      response.cookies.delete(cookie.name, { path: '/', domain: 'localhost' })
    })
    
    // Specifically target NextAuth cookies with various naming patterns
    const nextAuthCookiePatterns = [
      'next-auth.session-token',
      '__Secure-next-auth.session-token',
      'next-auth.csrf-token',
      '__Host-next-auth.csrf-token',
      'next-auth.callback-url',
      '__Secure-next-auth.callback-url',
      'next-auth.pkce.code_verifier',
      '__Secure-next-auth.pkce.code_verifier'
    ]
    
    nextAuthCookiePatterns.forEach(cookieName => {
      response.cookies.delete(cookieName)
      response.cookies.delete(cookieName, { path: '/' })
      response.cookies.delete(cookieName, { path: '/', domain: 'localhost' })
      response.cookies.delete(cookieName, { path: '/', secure: true })
      response.cookies.delete(cookieName, { path: '/', secure: true, sameSite: 'lax' })
    })
    
    // Set cache control headers to prevent caching
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate')
    response.headers.set('Pragma', 'no-cache')
    response.headers.set('Expires', '0')
    
    return response
  } catch (error) {
    console.error('Error clearing all sessions:', error)
    return NextResponse.json({
      success: false,
      error: 'Failed to clear sessions'
    }, { status: 500 })
  }
}
