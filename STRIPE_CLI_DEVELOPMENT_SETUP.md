# Stripe CLI Development Setup Guide

This guide will help you set up Stripe CLI for local development testing with webhooks.

## Prerequisites

- Stripe CLI installed on your system
- Your application running locally on `http://localhost:3000`
- Stripe account with test API keys

## Step 1: Login to Stripe CLI

First, authenticate with your Stripe account:

```bash
stripe login
```

This will open your browser and ask you to allow access to your Stripe account.

## Step 2: Forward Webhooks to Local Development

Run this command to forward Stripe webhooks to your local development server:

```bash
stripe listen --forward-to localhost:3000/api/webhooks/stripe
```

This command will:
- Start listening for webhook events from Stripe
- Forward them to your local webhook endpoint
- Display a webhook signing secret

## Step 3: Copy the Webhook Secret

When you run the `stripe listen` command, you'll see output like this:

```
> Ready! Your webhook signing secret is whsec_1234567890abcdef...
```

Copy this webhook secret (starts with `whsec_`).

## Step 4: Update Your Environment Variables

Add the webhook secret to your `.env` file:

```env
# Add or update this line in your .env file
STRIPE_WEBHOOK_SECRET="whsec_1234567890abcdef..."
```

**Important:** Replace the example secret with the actual secret from Step 3.

## Step 5: Restart Your Development Server

After updating the environment variable, restart your Next.js development server:

```bash
# Stop the current server (Ctrl+C)
# Then restart it
npm run dev
```

## Step 6: Test the Webhook

### Option 1: Trigger Test Events

In a new terminal, you can trigger test webhook events:

```bash
# Test a successful payment
stripe trigger payment_intent.succeeded

# Test a failed payment
stripe trigger payment_intent.payment_failed

# Test subscription events
stripe trigger customer.subscription.created
stripe trigger invoice.payment_succeeded
```

### Option 2: Make a Test Purchase

1. Go to your local application: `http://localhost:3000`
2. Add products to cart
3. Go through the checkout process
4. Use Stripe test card numbers:
   - **Success:** `****************`
   - **Decline:** `****************`
   - **Requires authentication:** `****************`

## Expected Output

When webhooks are working correctly, you should see:

### In the Stripe CLI terminal:
```
2024-01-15 10:30:45   --> payment_intent.succeeded [evt_1234...]
2024-01-15 10:30:45  <--  [200] POST http://localhost:3000/api/webhooks/stripe [evt_1234...]
```

### In your application logs:
```
Payment successful: pi_1234567890
Order confirmation email sent to: <EMAIL>
```

## Troubleshooting

### Common Issues:

1. **"Invalid signature" error**
   - Make sure you copied the correct webhook secret
   - Restart your development server after updating `.env`

2. **"Connection refused" error**
   - Ensure your Next.js app is running on `http://localhost:3000`
   - Check that the webhook endpoint exists at `/api/webhooks/stripe`

3. **Webhook not receiving events**
   - Verify the Stripe CLI is running with `stripe listen`
   - Check that you're using the correct webhook secret

### Debug Commands:

```bash
# Check Stripe CLI status
stripe status

# View recent webhook events
stripe events list --limit 10

# Test webhook endpoint directly
curl -X POST http://localhost:3000/api/webhooks/stripe \
  -H "Content-Type: application/json" \
  -d '{"test": "webhook"}'
```

## Production Setup

For production deployment, you'll need to:

1. Create a webhook endpoint in Stripe Dashboard
2. Set the endpoint URL to: `https://yourdomain.com/api/webhooks/stripe`
3. Select the events you want to receive
4. Copy the production webhook secret to your production environment

## Events Handled

Your application currently handles these webhook events:

- `payment_intent.succeeded` - Payment completed successfully
- `payment_intent.payment_failed` - Payment failed
- `invoice.payment_succeeded` - Subscription payment succeeded
- `customer.subscription.created` - New subscription created
- `customer.subscription.updated` - Subscription modified
- `customer.subscription.deleted` - Subscription cancelled

## Next Steps

Once webhooks are working:

1. Test the complete purchase flow
2. Verify order completion emails are sent
3. Check that download access is granted
4. Test subscription flows if applicable

## Support

If you encounter issues:

1. Check the Stripe CLI logs
2. Review your application logs
3. Verify webhook signatures are being validated
4. Test with Stripe's test card numbers
