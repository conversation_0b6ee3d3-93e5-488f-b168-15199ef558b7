'use client'

import Link from 'next/link'
import Image from 'next/image'
import { StarIcon } from '@heroicons/react/24/outline'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { ProductWithRelations } from '@/types'

interface RelatedProductsProps {
  products: ProductWithRelations[]
}

export function RelatedProducts({ products }: RelatedProductsProps) {
  if (products.length === 0) {
    return null
  }

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl lg:text-3xl font-bold text-white mb-2">
          Related Products
        </h2>
        <p className="text-gray-400">
          You might also be interested in these products
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {products.map((product) => (
          <div
            key={product.id}
            className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden hover:border-yellow-400/50 transition-all duration-300 group"
          >
            <div className="relative aspect-square overflow-hidden">
              <Image
                src={product.images?.[0] || '/images/placeholder-product.jpg'}
                alt={product.name}
                fill
                className="object-cover group-hover:scale-105 transition-transform duration-300"
              />
              {product.isOnSale && (
                <div className="absolute top-3 left-3">
                  <Badge variant="destructive" className="text-xs">
                    {Math.round(((product.originalPrice - product.salePrice) / product.originalPrice) * 100)}% OFF
                  </Badge>
                </div>
              )}
              {product.featured && (
                <div className="absolute top-3 right-3">
                  <Badge className="bg-yellow-400 text-black text-xs">
                    Featured
                  </Badge>
                </div>
              )}
            </div>

            <div className="p-4 space-y-3">
              <div>
                <Badge variant="secondary" className="text-xs mb-2">
                  {product.category.name}
                </Badge>
                <h3 className="font-semibold text-white text-sm line-clamp-2 group-hover:text-yellow-400 transition-colors">
                  {product.name}
                </h3>
              </div>

              <div className="flex items-center space-x-1">
                {[...Array(5)].map((_, i) => (
                  <StarIcon
                    key={i}
                    className="h-3 w-3 text-yellow-400 fill-current"
                  />
                ))}
                <span className="text-xs text-gray-400 ml-1">(0)</span>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg font-bold text-yellow-400">
                      ${product.isOnSale ? product.salePrice : product.price}
                    </span>
                    {product.isOnSale && (
                      <span className="text-sm text-gray-400 line-through">
                        ${product.originalPrice}
                      </span>
                    )}
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Link href={`/products/${product.slug}`} className="flex-1">
                    <Button variant="outline" size="sm" className="w-full text-xs">
                      View Details
                    </Button>
                  </Link>
                  <Button size="sm" className="bg-yellow-400 hover:bg-yellow-500 text-black text-xs px-3">
                    Add to Cart
                  </Button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
