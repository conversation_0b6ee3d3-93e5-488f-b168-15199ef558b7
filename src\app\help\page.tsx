import Link from 'next/link'
import { 
  QuestionMarkCircleIcon,
  DocumentTextIcon,
  ChatBubbleLeftRightIcon,
  WrenchScrewdriverIcon,
  ArrowRightIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

export default function HelpCenterPage() {
  const helpCategories = [
    {
      title: 'Getting Started',
      description: 'Learn the basics of using our trading tools and platform',
      icon: WrenchScrewdriverIcon,
      articles: [
        'How to create your first account',
        'Understanding our product categories',
        'Making your first purchase',
        'Downloading your products'
      ]
    },
    {
      title: 'Installation & Setup',
      description: 'Step-by-step guides for installing Expert Advisors and Indicators',
      icon: DocumentTextIcon,
      articles: [
        'Installing MT4 Expert Advisors',
        'Installing MT5 Expert Advisors',
        'Setting up custom indicators',
        'Troubleshooting installation issues'
      ]
    },
    {
      title: 'Account & Billing',
      description: 'Manage your account, payments, and subscriptions',
      icon: QuestionMarkCircleIcon,
      articles: [
        'Managing your account settings',
        'Payment methods and billing',
        'Refund and return policy',
        'Subscription management'
      ]
    },
    {
      title: 'Technical Support',
      description: 'Get help with technical issues and troubleshooting',
      icon: ChatBubbleLeftRightIcon,
      articles: [
        'Common error messages',
        'Performance optimization',
        'Compatibility issues',
        'Contact technical support'
      ]
    }
  ]

  const popularArticles = [
    'How to install Expert Advisors on MT4',
    'Understanding risk management settings',
    'Optimizing EA parameters for your account',
    'Troubleshooting connection issues',
    'Setting up VPS for 24/7 trading'
  ]

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-white mb-4">Help Center</h1>
          <p className="text-xl text-gray-300 mb-8">
            Find answers to your questions and get the most out of our trading tools
          </p>
          
          {/* Search Bar */}
          <div className="max-w-2xl mx-auto relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search for help articles..."
              className="trading-input pl-10 w-full"
            />
          </div>
        </div>

        {/* Quick Links */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          <Link 
            href="/help/installation"
            className="bg-gradient-to-r from-yellow-400 to-yellow-500 text-black p-6 rounded-xl hover:from-yellow-500 hover:to-yellow-600 transition-all duration-200 group"
          >
            <WrenchScrewdriverIcon className="w-8 h-8 mb-3" />
            <h3 className="text-lg font-semibold mb-2">Installation Guide</h3>
            <p className="text-sm opacity-90">Complete setup instructions</p>
            <ArrowRightIcon className="w-4 h-4 mt-2 group-hover:translate-x-1 transition-transform" />
          </Link>
          
          <Link 
            href="/help/faq"
            className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl hover:border-yellow-400/50 transition-all duration-200 group"
          >
            <QuestionMarkCircleIcon className="w-8 h-8 mb-3 text-yellow-400" />
            <h3 className="text-lg font-semibold mb-2 text-white">FAQ</h3>
            <p className="text-sm text-gray-300">Frequently asked questions</p>
            <ArrowRightIcon className="w-4 h-4 mt-2 text-gray-400 group-hover:translate-x-1 transition-transform" />
          </Link>
          
          <Link 
            href="/help/chat"
            className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 p-6 rounded-xl hover:border-yellow-400/50 transition-all duration-200 group"
          >
            <ChatBubbleLeftRightIcon className="w-8 h-8 mb-3 text-yellow-400" />
            <h3 className="text-lg font-semibold mb-2 text-white">Live Chat</h3>
            <p className="text-sm text-gray-300">Get instant support</p>
            <ArrowRightIcon className="w-4 h-4 mt-2 text-gray-400 group-hover:translate-x-1 transition-transform" />
          </Link>
        </div>

        {/* Help Categories */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
          {helpCategories.map((category, index) => (
            <div key={index} className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
              <div className="flex items-center mb-4">
                <category.icon className="w-8 h-8 text-yellow-400 mr-3" />
                <div>
                  <h3 className="text-xl font-semibold text-white">{category.title}</h3>
                  <p className="text-gray-300 text-sm">{category.description}</p>
                </div>
              </div>
              <ul className="space-y-2">
                {category.articles.map((article, articleIndex) => (
                  <li key={articleIndex}>
                    <Link 
                      href={`/help/article/${article.toLowerCase().replace(/\s+/g, '-')}`}
                      className="text-gray-300 hover:text-yellow-400 transition-colors duration-200 text-sm flex items-center group"
                    >
                      <ArrowRightIcon className="w-3 h-3 mr-2 group-hover:translate-x-1 transition-transform" />
                      {article}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* Popular Articles */}
        <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
          <h3 className="text-xl font-semibold text-white mb-4">Popular Articles</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {popularArticles.map((article, index) => (
              <Link 
                key={index}
                href={`/help/article/${article.toLowerCase().replace(/\s+/g, '-')}`}
                className="text-gray-300 hover:text-yellow-400 transition-colors duration-200 text-sm flex items-center group p-3 rounded-lg hover:bg-gray-800/50"
              >
                <ArrowRightIcon className="w-3 h-3 mr-2 group-hover:translate-x-1 transition-transform" />
                {article}
              </Link>
            ))}
          </div>
        </div>

        {/* Contact Support */}
        <div className="text-center mt-12">
          <h3 className="text-xl font-semibold text-white mb-4">Still need help?</h3>
          <p className="text-gray-300 mb-6">
            Can't find what you're looking for? Our support team is here to help.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link 
              href="/contact"
              className="btn-secondary"
            >
              Contact Support
            </Link>
            <Link 
              href="/help/chat"
              className="btn-primary"
            >
              Start Live Chat
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
