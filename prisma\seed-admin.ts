import { PrismaClient, UserRole } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function seedAdmin() {
  try {
    console.log('🌱 Seeding admin user...')

    // Check if admin user already exists
    const existingAdmin = await prisma.user.findUnique({
      where: { email: '<EMAIL>' }
    })

    if (existingAdmin) {
      console.log('✅ Admin user already exists')
      
      // Update password if needed
      const hashedPassword = await bcrypt.hash('admin123', 12)
      await prisma.user.update({
        where: { email: '<EMAIL>' },
        data: {
          password: hashedPassword,
          role: UserRole.ADMIN,
          isActive: true,
          emailVerified: new Date()
        }
      })
      
      console.log('✅ Admin user updated with fresh credentials')
      return
    }

    // Create admin user
    const hashedPassword = await bcrypt.hash('admin123', 12)
    
    const admin = await prisma.user.create({
      data: {
        email: '<EMAIL>',
        firstName: 'Admin',
        lastName: 'User',
        password: hashedPassword,
        role: UserRole.ADMIN,
        isActive: true,
        emailVerified: new Date()
      }
    })

    console.log('✅ Admin user created successfully')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 Password: admin123')
    
  } catch (error) {
    console.error('❌ Error seeding admin user:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

seedAdmin()
  .catch((error) => {
    console.error(error)
    process.exit(1)
  })
