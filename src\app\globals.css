@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-500;
}

/* Trading theme specific styles */
.trading-gradient {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.trading-gradient-secondary {
  background: linear-gradient(135deg, #ff6900 0%, #cf2e2e 100%);
}

.trading-gradient-gold {
  background: linear-gradient(135deg, #FFD700 0%, #FFA500 100%);
}

.trading-card {
  @apply bg-white dark:bg-gray-900 border border-gray-200 dark:border-gray-700 rounded-lg shadow-card hover:shadow-card-hover transition-all duration-300;
}

.trading-button {
  @apply px-6 py-3 rounded-full font-semibold text-white transition-all duration-300 transform hover:scale-105 active:scale-95;
}

.trading-button-primary {
  @apply trading-button trading-gradient hover:shadow-glow;
}

.trading-button-secondary {
  @apply trading-button trading-gradient-secondary hover:shadow-glow;
}

.trading-input {
  @apply w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200;
}

/* Animation classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading spinner */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
}

/* Glassmorphism effect */
.glass {
  @apply bg-white/10 backdrop-blur-md border border-white/20;
}

/* Rich Text Editor Styles */
.rich-text-editor {
  color: #ffffff !important;
  caret-color: #fbbf24 !important;
}

.rich-text-editor * {
  color: inherit !important;
}

.rich-text-editor p,
.rich-text-editor div,
.rich-text-editor span,
.rich-text-editor h1,
.rich-text-editor h2,
.rich-text-editor h3,
.rich-text-editor h4,
.rich-text-editor h5,
.rich-text-editor h6,
.rich-text-editor li,
.rich-text-editor ul,
.rich-text-editor ol,
.rich-text-editor blockquote {
  color: #ffffff !important;
}

.rich-text-editor a {
  color: #fbbf24 !important;
  text-decoration: underline;
}

.rich-text-editor strong,
.rich-text-editor b {
  color: #ffffff !important;
  font-weight: bold;
}

.rich-text-editor em,
.rich-text-editor i {
  color: #ffffff !important;
  font-style: italic;
}

.rich-text-editor::placeholder {
  color: #9ca3af !important;
}

.rich-text-editor:empty::before {
  content: attr(data-placeholder);
  color: #9ca3af;
  pointer-events: none;
  font-style: italic;
}

.rich-text-editor:focus:empty::before {
  color: #6b7280;
}

/* Premium card effects */
.premium-card {
  @apply relative overflow-hidden;
}

.premium-card::before {
  content: '';
  @apply absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent transform -skew-x-12 -translate-x-full transition-transform duration-1000;
}

.premium-card:hover::before {
  @apply translate-x-full;
}
