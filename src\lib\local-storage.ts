import { writeFile, mkdir, unlink, access } from 'fs/promises'
import { join } from 'path'
import { randomUUID } from 'crypto'

export interface LocalUploadResult {
  filename: string
  url: string
  size: number
  path: string
}

// Allowed file types for images
export const ALLOWED_IMAGE_TYPES = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
export const MAX_IMAGE_SIZE = 5 * 1024 * 1024 // 5MB

export function generateImageFilename(originalName: string): string {
  const extension = originalName.substring(originalName.lastIndexOf('.'))
  const timestamp = Date.now()
  const uuid = randomUUID().substring(0, 8)
  return `${timestamp}-${uuid}${extension}`
}

export function getImageExtension(filename: string): string {
  return filename.substring(filename.lastIndexOf('.')).toLowerCase()
}

export function isValidImageType(filename: string): boolean {
  const extension = getImageExtension(filename)
  return ALLOWED_IMAGE_TYPES.includes(extension)
}

export async function saveImageToLocal(
  file: Buffer,
  originalName: string,
  subfolder: string = 'products'
): Promise<LocalUploadResult> {
  try {
    // Validate file type
    if (!isValidImageType(originalName)) {
      throw new Error(`Invalid file type. Allowed types: ${ALLOWED_IMAGE_TYPES.join(', ')}`)
    }

    // Validate file size
    if (file.length > MAX_IMAGE_SIZE) {
      throw new Error(`File too large. Maximum size: ${Math.round(MAX_IMAGE_SIZE / 1024 / 1024)}MB`)
    }

    // Generate unique filename
    const filename = generateImageFilename(originalName)
    
    // Create directory path
    const uploadDir = join(process.cwd(), 'public', 'uploads', subfolder)
    const filePath = join(uploadDir, filename)
    
    // Ensure directory exists
    await mkdir(uploadDir, { recursive: true })
    
    // Write file
    await writeFile(filePath, file)
    
    // Return result
    return {
      filename,
      url: `/uploads/${subfolder}/${filename}`,
      size: file.length,
      path: filePath
    }
  } catch (error) {
    console.error('Error saving image to local storage:', error)
    throw new Error('Failed to save image')
  }
}

export async function deleteImageFromLocal(url: string): Promise<void> {
  try {
    // Extract path from URL (remove leading slash)
    const relativePath = url.startsWith('/') ? url.substring(1) : url
    const filePath = join(process.cwd(), 'public', relativePath)
    
    // Check if file exists
    try {
      await access(filePath)
    } catch {
      // File doesn't exist, nothing to delete
      return
    }
    
    // Delete file
    await unlink(filePath)
  } catch (error) {
    console.error('Error deleting image from local storage:', error)
    throw new Error('Failed to delete image')
  }
}

export function getImageUrl(filename: string, subfolder: string = 'products'): string {
  return `/uploads/${subfolder}/${filename}`
}

export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// Utility function to clean up old images
export async function cleanupOldImages(maxAgeHours: number = 24): Promise<void> {
  try {
    const { readdir, stat } = await import('fs/promises')
    const uploadDir = join(process.cwd(), 'public', 'uploads', 'products')
    
    try {
      const files = await readdir(uploadDir)
      const now = Date.now()
      const maxAge = maxAgeHours * 60 * 60 * 1000 // Convert to milliseconds
      
      for (const file of files) {
        const filePath = join(uploadDir, file)
        const stats = await stat(filePath)
        
        if (now - stats.mtime.getTime() > maxAge) {
          await unlink(filePath)
          console.log(`Cleaned up old image: ${file}`)
        }
      }
    } catch (error) {
      // Directory doesn't exist or other error, ignore
    }
  } catch (error) {
    console.error('Error during cleanup:', error)
  }
}
