import { NextRequest, NextResponse } from 'next/server'
import { generatePasswordResetToken } from '@/lib/auth-utils'
import { sendPasswordResetEmail } from '@/lib/email'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const forgotPasswordSchema = z.object({
  email: z.string().email('Invalid email address'),
})

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    
    // Validate input
    const { email } = forgotPasswordSchema.parse(body)
    
    // Check if user exists
    const user = await prisma.user.findUnique({
      where: { email },
      select: { id: true, firstName: true, email: true }
    })

    if (user) {
      // Generate reset token
      const token = await generatePasswordResetToken(email)

      // Send password reset email
      try {
        await sendPasswordResetEmail(email, {
          firstName: user.firstName,
          resetUrl: `${process.env.NEXTAUTH_URL || process.env.SITE_URL}/auth/reset-password?token=${token}`,
          expiresIn: '24 hours'
        })
        console.log(`Password reset email sent to: ${email}`)
      } catch (emailError) {
        console.error('Failed to send password reset email:', emailError)
        // Don't reveal if email failed for security
      }
    }

    // Always return success for security (don't reveal if user exists)
    return NextResponse.json({
      success: true,
      message: 'If an account with that email exists, a password reset email has been sent'
    })
    
  } catch (error: any) {
    console.error('Forgot password error:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }
    
    // Don't reveal if user exists or not for security
    return NextResponse.json({
      success: true,
      message: 'If an account with that email exists, a password reset email has been sent'
    })
  }
}
