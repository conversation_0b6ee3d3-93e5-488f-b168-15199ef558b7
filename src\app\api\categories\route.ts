import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'
import { z } from 'zod'

const categorySchema = z.object({
  name: z.string().min(1, 'Category name is required'),
  slug: z.string().min(1, 'Category slug is required'),
  description: z.string().optional(),
  image: z.string().optional(),
  isActive: z.boolean().default(true),
  sortOrder: z.number().default(0),
})

// GET /api/categories - List all categories
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const includeProducts = searchParams.get('includeProducts') === 'true'
    const activeOnly = searchParams.get('activeOnly') !== 'false' // Default to true

    const where = activeOnly ? { isActive: true } : {}

    const categories = await prisma.category.findMany({
      where,
      include: {
        products: includeProducts ? {
          where: { status: 'PUBLISHED' },
          select: {
            id: true,
            name: true,
            slug: true,
            price: true,
            images: true,
            featured: true
          }
        } : false,
        _count: {
          select: {
            products: {
              where: { status: 'PUBLISHED' }
            }
          }
        }
      },
      orderBy: [
        { sortOrder: 'asc' },
        { name: 'asc' }
      ]
    })

    return NextResponse.json({
      success: true,
      data: categories
    })

  } catch (error: any) {
    console.error('Error fetching categories:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch categories'
    }, { status: 500 })
  }
}

// POST /api/categories - Create new category (Admin only)
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const validatedData = categorySchema.parse(body)

    // Check if slug already exists
    const existingCategory = await prisma.category.findUnique({
      where: { slug: validatedData.slug }
    })

    if (existingCategory) {
      return NextResponse.json({
        success: false,
        message: 'Category slug already exists'
      }, { status: 400 })
    }

    // Check if name already exists
    const existingName = await prisma.category.findUnique({
      where: { name: validatedData.name }
    })

    if (existingName) {
      return NextResponse.json({
        success: false,
        message: 'Category name already exists'
      }, { status: 400 })
    }

    const category = await prisma.category.create({
      data: {
        ...validatedData,
        description: validatedData.description || null,
        image: validatedData.image || null,
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Category created successfully',
      data: category
    }, { status: 201 })

  } catch (error: any) {
    console.error('Error creating category:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to create category'
    }, { status: 500 })
  }
}
