'use client'

import { useState, useEffect } from 'react'
import { toast } from 'react-hot-toast'
import {
  EnvelopeIcon,
  PaperAirplaneIcon,
  UserPlusIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  PencilIcon,
  PlusIcon,
  TrashIcon
} from '@heroicons/react/24/outline'
import { EmailTemplateEditor } from '@/components/admin/email-template-editor'
import { DeleteConfirmationModal } from '@/components/ui/delete-confirmation-modal'
import { EmailTemplateType } from '@prisma/client'

interface EmailTemplate {
  id: string
  name: string
  subject: string
  htmlContent: string
  textContent?: string
  type: EmailTemplateType
  isActive: boolean
  variables?: Record<string, string>
  createdAt: string
  updatedAt: string
  _count?: {
    emailLogs: number
  }
}

interface EmailStats {
  overview: {
    totalSent: number
    totalDelivered: number
    totalOpened: number
    totalClicked: number
    totalBounced: number
    totalFailed: number
    deliveryRate: string
    openRate: string
    clickRate: string
    bounceRate: string
  }
  byType: {
    welcomeEmails: number
    orderConfirmations: number
    passwordResets: number
  }
}

export default function EmailsPage() {
  const [loading, setLoading] = useState<string | null>(null)
  const [testEmail, setTestEmail] = useState('')
  const [testName, setTestName] = useState('')
  const [selectedTemplate, setSelectedTemplate] = useState<string>('welcome-template')
  const [templates, setTemplates] = useState<EmailTemplate[]>([])
  const [stats, setStats] = useState<EmailStats | null>(null)
  const [templatesLoading, setTemplatesLoading] = useState(true)
  const [statsLoading, setStatsLoading] = useState(true)

  const [editorModal, setEditorModal] = useState<{
    isOpen: boolean
    template: EmailTemplate | null
    isNew: boolean
  }>({
    isOpen: false,
    template: null,
    isNew: false
  })

  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean
    template: EmailTemplate | null
    loading: boolean
  }>({
    isOpen: false,
    template: null,
    loading: false
  })

  useEffect(() => {
    fetchTemplates()
    fetchStats()
  }, [])

  const fetchTemplates = async () => {
    try {
      const response = await fetch('/api/admin/email-templates')
      if (response.ok) {
        const data = await response.json()
        setTemplates(data.data || [])
      }
    } catch (error) {
      console.error('Error fetching templates:', error)
      toast.error('Failed to load email templates')
    } finally {
      setTemplatesLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/email-stats')
      if (response.ok) {
        const data = await response.json()
        setStats(data.data)
      }
    } catch (error) {
      console.error('Error fetching stats:', error)
      toast.error('Failed to load email statistics')
    } finally {
      setStatsLoading(false)
    }
  }

  const handleEditTemplate = (template: EmailTemplate) => {
    setEditorModal({
      isOpen: true,
      template,
      isNew: false
    })
  }

  const handleCreateTemplate = () => {
    setEditorModal({
      isOpen: true,
      template: null,
      isNew: true
    })
  }

  const handleDeleteTemplate = (template: EmailTemplate) => {
    setDeleteModal({
      isOpen: true,
      template,
      loading: false
    })
  }

  const confirmDeleteTemplate = async () => {
    if (!deleteModal.template) return

    setDeleteModal(prev => ({ ...prev, loading: true }))

    try {
      const response = await fetch(`/api/admin/email-templates/${deleteModal.template.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setTemplates(templates.filter(t => t.id !== deleteModal.template!.id))
        toast.success('Template deleted successfully!')
        setDeleteModal({ isOpen: false, template: null, loading: false })
      } else {
        const data = await response.json()
        toast.error(data.message || 'Failed to delete template')
        setDeleteModal(prev => ({ ...prev, loading: false }))
      }
    } catch (error) {
      console.error('Error deleting template:', error)
      toast.error('Failed to delete template')
      setDeleteModal(prev => ({ ...prev, loading: false }))
    }
  }

  const handleTemplateSaved = (savedTemplate: EmailTemplate) => {
    if (editorModal.isNew) {
      setTemplates([savedTemplate, ...templates])
    } else {
      setTemplates(templates.map(t => t.id === savedTemplate.id ? savedTemplate : t))
    }
    fetchStats() // Refresh stats
  }

  const sendTestEmail = async (templateId: string) => {
    if (!testEmail || !testName) {
      toast.error('Please fill in email and name')
      return
    }

    const template = templates.find(t => t.id === templateId)
    if (!template) {
      toast.error('Template not found')
      return
    }

    if (!template.isActive) {
      toast.error('Cannot send test email for inactive template')
      return
    }

    setLoading(templateId)
    try {
      const response = await fetch('/api/admin/email-templates/test', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          templateId,
          to: testEmail,
          variables: {
            firstName: testName,
            email: testEmail,
            resetUrl: `${window.location.origin}/auth/reset-password?token=test-token`,
            loginUrl: `${window.location.origin}/auth/signin`,
            orderNumber: 'TEST-12345',
            total: '99.99',
            customerName: testName,
            downloadUrl: `${window.location.origin}/downloads/test`,
            expiresIn: '24 hours'
          }
        })
      })

      const data = await response.json()
      if (data.success) {
        toast.success(`Test email sent successfully to ${testEmail}!`)
        setTestEmail('')
        setTestName('')
      } else {
        toast.error(data.message || 'Failed to send test email')
      }
    } catch (error) {
      console.error('Error sending test email:', error)
      toast.error('Failed to send test email')
    } finally {
      setLoading(null)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="border-b border-gray-700 pb-4">
        <h1 className="text-2xl font-bold text-white flex items-center gap-2">
          <EnvelopeIcon className="h-8 w-8 text-yellow-400" />
          Email Management
        </h1>
        <p className="text-gray-400 mt-1">
          Manage email templates and send test emails
        </p>
      </div>

      {/* Test Email Section */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4 flex items-center gap-2">
          <PaperAirplaneIcon className="h-6 w-6 text-blue-400" />
          Send Test Email
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Test Email Address
            </label>
            <input
              type="email"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-2">
              Test Name
            </label>
            <input
              type="text"
              value={testName}
              onChange={(e) => setTestName(e.target.value)}
              placeholder="John Doe"
              className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Select Template
          </label>
          <select
            value={selectedTemplate}
            onChange={(e) => setSelectedTemplate(e.target.value)}
            className="w-full px-3 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
          >
            {templates.map((template) => (
              <option key={template.id} value={template.id}>
                {template.name}
              </option>
            ))}
          </select>
        </div>

        <button
          onClick={() => sendTestEmail(selectedTemplate)}
          disabled={loading === selectedTemplate || !testEmail || !testName}
          className="bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-medium py-2 px-4 rounded-md transition-colors duration-200 flex items-center gap-2"
        >
          {loading === selectedTemplate ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-black"></div>
              Sending...
            </>
          ) : (
            <>
              <PaperAirplaneIcon className="h-4 w-4" />
              Send Test Email
            </>
          )}
        </button>
      </div>

      {/* Email Templates */}
      <div className="bg-gray-800 rounded-lg p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-white flex items-center gap-2">
            <InformationCircleIcon className="h-6 w-6 text-green-400" />
            Email Templates
          </h2>
          <button
            onClick={handleCreateTemplate}
            className="bg-yellow-500 hover:bg-yellow-600 text-black font-medium py-2 px-4 rounded-md transition-colors duration-200 flex items-center gap-2"
          >
            <PlusIcon className="h-4 w-4" />
            Create Template
          </button>
        </div>

        {templatesLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {templates.map((template) => (
              <div key={template.id} className="bg-gray-700 rounded-lg p-4 border border-gray-600">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-white">{template.name}</h3>
                  <div className="flex items-center gap-2">
                    <span className={`px-2 py-1 text-xs rounded-full ${
                      template.type === EmailTemplateType.WELCOME ? 'bg-green-100 text-green-800' :
                      template.type === EmailTemplateType.ORDER_CONFIRMATION ? 'bg-blue-100 text-blue-800' :
                      template.type === EmailTemplateType.PASSWORD_RESET ? 'bg-yellow-100 text-yellow-800' :
                      'bg-gray-100 text-gray-800'
                    }`}>
                      {template.type.toLowerCase().replace('_', ' ')}
                    </span>
                    {!template.isActive && (
                      <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800">
                        Inactive
                      </span>
                    )}
                  </div>
                </div>

                <div className="text-xs text-gray-400 mb-3">
                  <strong>Subject:</strong> {template.subject}
                </div>

                {template._count && (
                  <div className="text-xs text-gray-400 mb-3">
                    <strong>Emails sent:</strong> {template._count.emailLogs}
                  </div>
                )}

                <div className="flex gap-2">
                  <button
                    onClick={() => {
                      setSelectedTemplate(template.id)
                      if (testEmail && testName) {
                        sendTestEmail(template.id)
                      }
                    }}
                    disabled={loading === template.id || !testEmail || !testName || !template.isActive}
                    className="text-xs bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-600 disabled:cursor-not-allowed text-black font-medium py-1 px-2 rounded transition-colors duration-200"
                  >
                    {loading === template.id ? 'Sending...' : 'Test'}
                  </button>
                  <button
                    onClick={() => handleEditTemplate(template)}
                    className="text-xs bg-blue-500 hover:bg-blue-600 text-white font-medium py-1 px-2 rounded transition-colors duration-200 flex items-center gap-1"
                  >
                    <PencilIcon className="h-3 w-3" />
                    Edit
                  </button>
                  <button
                    onClick={() => handleDeleteTemplate(template)}
                    className="text-xs bg-red-500 hover:bg-red-600 text-white font-medium py-1 px-2 rounded transition-colors duration-200 flex items-center gap-1"
                  >
                    <TrashIcon className="h-3 w-3" />
                    Delete
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Email Statistics */}
      <div className="bg-gray-800 rounded-lg p-6">
        <h2 className="text-xl font-semibold text-white mb-4">Email Statistics</h2>

        {statsLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
          </div>
        ) : stats ? (
          <>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Welcome Emails</p>
                    <p className="text-2xl font-bold text-white">{stats.byType.welcomeEmails}</p>
                  </div>
                  <UserPlusIcon className="h-8 w-8 text-green-400" />
                </div>
              </div>

              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Order Confirmations</p>
                    <p className="text-2xl font-bold text-white">{stats.byType.orderConfirmations}</p>
                  </div>
                  <EnvelopeIcon className="h-8 w-8 text-blue-400" />
                </div>
              </div>

              <div className="bg-gray-700 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm text-gray-400">Failed Emails</p>
                    <p className="text-2xl font-bold text-white">{stats.overview.totalFailed}</p>
                  </div>
                  <ExclamationTriangleIcon className="h-8 w-8 text-red-400" />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="bg-gray-700 rounded-lg p-4 text-center">
                <p className="text-sm text-gray-400">Total Sent</p>
                <p className="text-xl font-bold text-white">{stats.overview.totalSent}</p>
              </div>

              <div className="bg-gray-700 rounded-lg p-4 text-center">
                <p className="text-sm text-gray-400">Delivery Rate</p>
                <p className="text-xl font-bold text-green-400">{stats.overview.deliveryRate}%</p>
              </div>

              <div className="bg-gray-700 rounded-lg p-4 text-center">
                <p className="text-sm text-gray-400">Open Rate</p>
                <p className="text-xl font-bold text-blue-400">{stats.overview.openRate}%</p>
              </div>

              <div className="bg-gray-700 rounded-lg p-4 text-center">
                <p className="text-sm text-gray-400">Click Rate</p>
                <p className="text-xl font-bold text-purple-400">{stats.overview.clickRate}%</p>
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-400">Failed to load email statistics</p>
            <button
              onClick={fetchStats}
              className="mt-2 text-yellow-400 hover:text-yellow-300 underline"
            >
              Retry
            </button>
          </div>
        )}
      </div>

      {/* Modals */}
      <EmailTemplateEditor
        isOpen={editorModal.isOpen}
        onClose={() => setEditorModal({ isOpen: false, template: null, isNew: false })}
        onSave={handleTemplateSaved}
        template={editorModal.template}
        isNew={editorModal.isNew}
      />

      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, template: null, loading: false })}
        onConfirm={confirmDeleteTemplate}
        title="Delete Email Template"
        message="Are you sure you want to delete this email template? This action cannot be undone."
        itemName={deleteModal.template?.name || ''}
        loading={deleteModal.loading}
      />
    </div>
  )
}
