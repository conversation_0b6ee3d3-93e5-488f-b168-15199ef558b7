import { prisma } from '@/lib/prisma'
import { generateSecureDownloadUrl } from '@/lib/cloudflare-r2'
import crypto from 'crypto'

export interface DownloadToken {
  id: string
  token: string
  productId: string
  userId: string
  expiresAt: Date
  downloadCount: number
  maxDownloads: number
  isActive: boolean
}

export interface DownloadResult {
  success: boolean
  downloadUrl?: string
  error?: string
  remainingDownloads?: number
}

/**
 * Create a secure download token for a user's purchased product
 */
export async function createDownloadToken(
  userId: string,
  productId: string,
  maxDownloads: number = 5,
  expiryDays: number = 30
): Promise<DownloadToken> {
  try {
    // Check if user has purchased the product
    const purchase = await prisma.purchase.findFirst({
      where: {
        userId,
        productId,
        status: 'COMPLETED'
      }
    })

    if (!purchase) {
      throw new Error('Product not purchased or purchase not completed')
    }

    // Check if there's an existing active token
    const existingToken = await prisma.downloadToken.findFirst({
      where: {
        userId,
        productId,
        isActive: true,
        expiresAt: {
          gt: new Date()
        }
      }
    })

    if (existingToken) {
      return existingToken
    }

    // Generate a secure token
    const token = crypto.randomBytes(32).toString('hex')
    const expiresAt = new Date()
    expiresAt.setDate(expiresAt.getDate() + expiryDays)

    // Create new download token
    const downloadToken = await prisma.downloadToken.create({
      data: {
        token,
        userId,
        productId,
        expiresAt,
        downloadCount: 0,
        maxDownloads,
        isActive: true
      }
    })

    return downloadToken
  } catch (error) {
    console.error('Error creating download token:', error)
    throw new Error('Failed to create download token')
  }
}

/**
 * Generate a secure download URL using a download token
 */
export async function generateDownloadUrl(
  token: string,
  userAgent?: string,
  ipAddress?: string
): Promise<DownloadResult> {
  try {
    // Find and validate the token
    const downloadToken = await prisma.downloadToken.findUnique({
      where: { token },
      include: {
        product: true,
        user: true
      }
    })

    if (!downloadToken) {
      return {
        success: false,
        error: 'Invalid download token'
      }
    }

    // Check if token is active
    if (!downloadToken.isActive) {
      return {
        success: false,
        error: 'Download token has been deactivated'
      }
    }

    // Check if token has expired
    if (downloadToken.expiresAt < new Date()) {
      await prisma.downloadToken.update({
        where: { id: downloadToken.id },
        data: { isActive: false }
      })

      return {
        success: false,
        error: 'Download token has expired'
      }
    }

    // Check download limit
    if (downloadToken.downloadCount >= downloadToken.maxDownloads) {
      return {
        success: false,
        error: 'Download limit exceeded'
      }
    }

    // Check if product has files
    if (!downloadToken.product.fileKey) {
      return {
        success: false,
        error: 'Product file not available'
      }
    }

    // Generate secure download URL (valid for 1 hour)
    const downloadUrl = await generateSecureDownloadUrl(
      downloadToken.product.fileKey,
      downloadToken.product.fileName || `${downloadToken.product.name}.zip`,
      3600 // 1 hour
    )

    // Record the download attempt
    await prisma.downloadToken.update({
      where: { id: downloadToken.id },
      data: {
        downloadCount: downloadToken.downloadCount + 1,
        lastDownloadAt: new Date()
      }
    })

    // Log the download
    await prisma.downloadLog.create({
      data: {
        tokenId: downloadToken.id,
        userId: downloadToken.userId,
        productId: downloadToken.productId,
        ipAddress: ipAddress || 'unknown',
        userAgent: userAgent || 'unknown',
        downloadedAt: new Date()
      }
    })

    return {
      success: true,
      downloadUrl,
      remainingDownloads: downloadToken.maxDownloads - (downloadToken.downloadCount + 1)
    }
  } catch (error) {
    console.error('Error generating download URL:', error)
    return {
      success: false,
      error: 'Failed to generate download URL'
    }
  }
}

/**
 * Get download status for a user's product
 */
export async function getDownloadStatus(
  userId: string,
  productId: string
): Promise<{
  hasAccess: boolean
  token?: string
  downloadCount: number
  maxDownloads: number
  remainingDownloads: number
  expiresAt?: Date
  isExpired: boolean
}> {
  try {
    // Check if user has purchased the product
    const purchase = await prisma.purchase.findFirst({
      where: {
        userId,
        productId,
        status: 'COMPLETED'
      }
    })

    if (!purchase) {
      return {
        hasAccess: false,
        downloadCount: 0,
        maxDownloads: 0,
        remainingDownloads: 0,
        isExpired: true
      }
    }

    // Get the latest download token
    const downloadToken = await prisma.downloadToken.findFirst({
      where: {
        userId,
        productId,
        isActive: true
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    if (!downloadToken) {
      return {
        hasAccess: true,
        downloadCount: 0,
        maxDownloads: 0,
        remainingDownloads: 0,
        isExpired: false
      }
    }

    const isExpired = downloadToken.expiresAt < new Date()
    const remainingDownloads = Math.max(0, downloadToken.maxDownloads - downloadToken.downloadCount)

    return {
      hasAccess: true,
      token: downloadToken.token,
      downloadCount: downloadToken.downloadCount,
      maxDownloads: downloadToken.maxDownloads,
      remainingDownloads,
      expiresAt: downloadToken.expiresAt,
      isExpired
    }
  } catch (error) {
    console.error('Error getting download status:', error)
    return {
      hasAccess: false,
      downloadCount: 0,
      maxDownloads: 0,
      remainingDownloads: 0,
      isExpired: true
    }
  }
}

/**
 * Revoke a download token
 */
export async function revokeDownloadToken(tokenId: string): Promise<boolean> {
  try {
    await prisma.downloadToken.update({
      where: { id: tokenId },
      data: { isActive: false }
    })
    return true
  } catch (error) {
    console.error('Error revoking download token:', error)
    return false
  }
}

/**
 * Clean up expired tokens
 */
export async function cleanupExpiredTokens(): Promise<number> {
  try {
    const result = await prisma.downloadToken.updateMany({
      where: {
        expiresAt: {
          lt: new Date()
        },
        isActive: true
      },
      data: {
        isActive: false
      }
    })

    return result.count
  } catch (error) {
    console.error('Error cleaning up expired tokens:', error)
    return 0
  }
}
