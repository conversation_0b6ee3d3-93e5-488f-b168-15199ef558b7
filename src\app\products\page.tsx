import { Metadata } from 'next'
import { Suspense } from 'react'
import { ProductsGrid } from '@/components/products/products-grid'
import { ProductsFilters } from '@/components/products/products-filters'
import { ProductsSearch } from '@/components/products/products-search'
import { LoadingSpinner } from '@/components/ui/loading-spinner'

export const metadata: Metadata = {
  title: 'Premium Forex Bots & Indicators - Forex Bot Zone',
  description: 'Browse our collection of premium Forex Expert Advisors and Indicators at special prices. Professional trading bots for MT4 and MT5.',
}

interface ProductsPageProps {
  searchParams: {
    category?: string
    search?: string
    minPrice?: string
    maxPrice?: string
    featured?: string
    onSale?: string
    tags?: string
    sortBy?: string
    sortOrder?: string
    page?: string
  }
}

export default function ProductsPage({ searchParams }: ProductsPageProps) {
  const filters = {
    category: searchParams.category,
    search: searchParams.search,
    minPrice: searchParams.minPrice ? parseFloat(searchParams.minPrice) : undefined,
    maxPrice: searchParams.maxPrice ? parseFloat(searchParams.maxPrice) : undefined,
    featured: searchParams.featured === 'true',
    onSale: searchParams.onSale === 'true',
    tags: searchParams.tags?.split(',') || [],
    sortBy: searchParams.sortBy || 'createdAt',
    sortOrder: (searchParams.sortOrder as 'asc' | 'desc') || 'desc',
  }

  const currentPage = parseInt(searchParams.page || '1')

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Premium Forex Bots & Indicators
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Professional trading tools at special prices. Find the perfect Expert Advisor or Indicator for your trading strategy.
          </p>
        </div>

        {/* Search */}
        <div className="mb-8">
          <ProductsSearch />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24">
              <ProductsFilters />
            </div>
          </div>

          {/* Products Grid */}
          <div className="lg:col-span-3">
            <Suspense fallback={<LoadingSpinner />}>
              <ProductsGrid filters={filters} currentPage={currentPage} />
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  )
}
