import { NextRequest, NextResponse } from 'next/server'
import { sendEmail } from '@/lib/email'

export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({
      success: false,
      message: 'This endpoint is only available in development'
    }, { status: 403 })
  }

  try {
    const { to, subject, message } = await request.json()
    
    if (!to || !subject || !message) {
      return NextResponse.json({
        success: false,
        message: 'to, subject, and message are required'
      }, { status: 400 })
    }
    
    await sendEmail({
      to,
      subject: `[TEST] ${subject}`,
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: #f59e0b; color: white; padding: 10px; text-align: center; border-radius: 5px; margin-bottom: 20px;">
            <strong>TEST EMAIL</strong>
          </div>
          <h2>${subject}</h2>
          <p style="white-space: pre-wrap;">${message}</p>
          <hr style="margin: 20px 0;">
          <p style="color: #666; font-size: 12px;">
            This is a test email sent from Forex Bot Zone development environment.
          </p>
        </div>
      `,
      text: `[TEST] ${subject}\n\n${message}\n\nThis is a test email from Forex Bot Zone development environment.`
    })
    
    return NextResponse.json({
      success: true,
      message: 'Test email sent successfully'
    })
    
  } catch (error: any) {
    console.error('Test email error:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to send test email'
    }, { status: 500 })
  }
}
