import { NextRequest, NextResponse } from 'next/server'
import { sendPasswordResetEmail } from '@/lib/email'
import { requireAdmin } from '@/lib/auth-utils'

export async function POST(request: NextRequest) {
  try {
    // Only admins can send password reset emails (for testing)
    await requireAdmin()
    
    const { email, firstName, resetUrl } = await request.json()
    
    if (!email || !firstName || !resetUrl) {
      return NextResponse.json({
        success: false,
        message: 'Email, firstName, and resetUrl are required'
      }, { status: 400 })
    }
    
    await sendPasswordResetEmail(email, {
      firstName,
      resetUrl,
      expiresIn: '24 hours'
    })
    
    return NextResponse.json({
      success: true,
      message: 'Password reset email sent successfully'
    })
    
  } catch (error: any) {
    console.error('Password reset email error:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to send password reset email'
    }, { status: 500 })
  }
}
