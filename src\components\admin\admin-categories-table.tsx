'use client'

import { useEffect, useState } from 'react'
import {
  PencilIcon,
  TrashIcon,
  PlusIcon
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'
import { Category } from '@prisma/client'
import { CategoryModal } from './category-modal'
import { DeleteConfirmationModal } from '@/components/ui/delete-confirmation-modal'
import { toast } from 'react-hot-toast'

interface CategoryWithCount extends Category {
  _count?: {
    products: number
  }
}

export function AdminCategoriesTable() {
  const [categories, setCategories] = useState<CategoryWithCount[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const [categoryModal, setCategoryModal] = useState<{
    isOpen: boolean
    category: CategoryWithCount | null
    isNew: boolean
  }>({
    isOpen: false,
    category: null,
    isNew: false
  })

  const [deleteModal, setDeleteModal] = useState<{
    isOpen: boolean
    category: CategoryWithCount | null
    loading: boolean
  }>({
    isOpen: false,
    category: null,
    loading: false
  })

  useEffect(() => {
    fetchCategories()
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await fetch('/api/admin/categories')
      const data = await response.json()

      if (response.ok && data.success) {
        setCategories(data.data || [])
        setError(null)
      } else {
        setError(data.message || 'Failed to fetch categories')
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
      setError('Failed to fetch categories')
    } finally {
      setLoading(false)
    }
  }

  const handleCreateCategory = () => {
    setCategoryModal({
      isOpen: true,
      category: null,
      isNew: true
    })
  }

  const handleEditCategory = (category: CategoryWithCount) => {
    setCategoryModal({
      isOpen: true,
      category,
      isNew: false
    })
  }

  const handleDeleteCategory = (category: CategoryWithCount) => {
    setDeleteModal({
      isOpen: true,
      category,
      loading: false
    })
  }

  const confirmDeleteCategory = async () => {
    if (!deleteModal.category) return

    setDeleteModal(prev => ({ ...prev, loading: true }))

    try {
      const response = await fetch(`/api/admin/categories/${deleteModal.category.id}`, {
        method: 'DELETE',
      })

      if (response.ok) {
        setCategories(categories.filter(c => c.id !== deleteModal.category!.id))
        toast.success('Category deleted successfully!')
        setDeleteModal({ isOpen: false, category: null, loading: false })
      } else {
        const data = await response.json()
        toast.error(data.message || 'Failed to delete category')
        setDeleteModal(prev => ({ ...prev, loading: false }))
      }
    } catch (error) {
      console.error('Error deleting category:', error)
      toast.error('Failed to delete category')
      setDeleteModal(prev => ({ ...prev, loading: false }))
    }
  }

  const handleCategorySaved = (savedCategory: Category) => {
    if (categoryModal.isNew) {
      setCategories([savedCategory as CategoryWithCount, ...categories])
    } else {
      setCategories(categories.map(c => c.id === savedCategory.id ? { ...savedCategory, _count: c._count } as CategoryWithCount : c))
    }
    fetchCategories() // Refresh to get updated counts
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
        <div className="text-center">
          <p className="text-red-400 mb-4">{error}</p>
          <Button
            onClick={() => window.location.reload()}
            className="bg-yellow-400 hover:bg-yellow-500 text-black"
          >
            Retry
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl overflow-hidden">
      <div className="px-6 py-4 border-b border-white/10 flex items-center justify-between">
        <h2 className="text-lg font-medium text-white">All Categories</h2>
        <Button
          onClick={handleCreateCategory}
          className="bg-yellow-400 hover:bg-yellow-500 text-black"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          Add Category
        </Button>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-700">
          <thead className="bg-gray-800/50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Slug
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Description
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Sort Order
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                Products
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-gray-700">
            {categories.map((category) => (
              <tr key={category.id} className="hover:bg-white/5">
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm font-medium text-white">{category.name}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-300">{category.slug}</div>
                </td>
                <td className="px-6 py-4">
                  <div className="text-sm text-gray-300 max-w-xs truncate">
                    {category.description}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-300">{category.sortOrder}</div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-300">
                    {category._count?.products || 0} products
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex items-center justify-end space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditCategory(category)}
                    >
                      <PencilIcon className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="text-red-400 hover:text-red-300"
                      onClick={() => handleDeleteCategory(category)}
                    >
                      <TrashIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {categories.length === 0 && (
        <div className="text-center py-12">
          <p className="text-gray-400">No categories found.</p>
        </div>
      )}

      {/* Modals */}
      <CategoryModal
        isOpen={categoryModal.isOpen}
        onClose={() => setCategoryModal({ isOpen: false, category: null, isNew: false })}
        onSave={handleCategorySaved}
        category={categoryModal.category}
        isNew={categoryModal.isNew}
      />

      <DeleteConfirmationModal
        isOpen={deleteModal.isOpen}
        onClose={() => setDeleteModal({ isOpen: false, category: null, loading: false })}
        onConfirm={confirmDeleteCategory}
        title="Delete Category"
        message="Are you sure you want to delete this category? This action cannot be undone and will affect all products in this category."
        itemName={deleteModal.category?.name || ''}
        loading={deleteModal.loading}
      />
    </div>
  )
}
