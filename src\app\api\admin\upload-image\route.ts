import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth-utils'
import { saveImageToLocal, deleteImageFromLocal, isValidImageType, MAX_IMAGE_SIZE } from '@/lib/local-storage'

// POST /api/admin/upload-image - Upload images to local storage
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()

    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string || 'product-image'

    if (!file) {
      return NextResponse.json({
        success: false,
        message: 'No file provided'
      }, { status: 400 })
    }

    // Validate file type
    if (!isValidImageType(file.name)) {
      return NextResponse.json({
        success: false,
        message: 'Invalid file type. Only JPG, PNG, GIF, and WebP images are allowed.'
      }, { status: 400 })
    }

    // Validate file size
    if (file.size > MAX_IMAGE_SIZE) {
      return NextResponse.json({
        success: false,
        message: `File too large. Maximum size: ${Math.round(MAX_IMAGE_SIZE / 1024 / 1024)}MB`
      }, { status: 400 })
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())
    
    // Determine subfolder based on type
    const subfolder = type === 'product-image' ? 'products' : 'general'
    
    // Save to local storage
    const uploadResult = await saveImageToLocal(buffer, file.name, subfolder)

    return NextResponse.json({
      success: true,
      message: 'Image uploaded successfully',
      data: {
        filename: uploadResult.filename,
        url: uploadResult.url,
        size: uploadResult.size,
        originalName: file.name,
        type: file.type
      }
    })

  } catch (error: any) {
    console.error('Error uploading image:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to upload image'
    }, { status: 500 })
  }
}

// DELETE /api/admin/upload-image - Delete image from local storage
export async function DELETE(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const imageUrl = searchParams.get('url')

    if (!imageUrl) {
      return NextResponse.json({
        success: false,
        message: 'Image URL is required'
      }, { status: 400 })
    }

    // Delete from local storage
    await deleteImageFromLocal(imageUrl)

    return NextResponse.json({
      success: true,
      message: 'Image deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting image:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to delete image'
    }, { status: 500 })
  }
}
