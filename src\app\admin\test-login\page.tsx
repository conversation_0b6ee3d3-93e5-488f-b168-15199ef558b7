'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export default function TestLoginPage() {
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)

  const testLogin = async () => {
    setLoading(true)
    setResult('')
    
    try {
      const response = await fetch('/api/admin/simple-login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: '<EMAIL>',
          password: 'admin123'
        }),
      })

      const data = await response.json()
      setResult(JSON.stringify(data, null, 2))
      
      if (data.success) {
        // Test verification
        const verifyResponse = await fetch('/api/admin/verify')
        const verifyData = await verifyResponse.json()
        setResult(prev => prev + '\n\nVerification:\n' + JSON.stringify(verifyData, null, 2))
      }
    } catch (error) {
      setResult('Error: ' + error)
    } finally {
      setLoading(false)
    }
  }

  const testVerify = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/verify')
      const data = await response.json()
      setResult(JSON.stringify(data, null, 2))
    } catch (error) {
      setResult('Error: ' + error)
    } finally {
      setLoading(false)
    }
  }

  const clearSession = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/admin/verify', { method: 'DELETE' })
      const data = await response.json()
      setResult(JSON.stringify(data, null, 2))
    } catch (error) {
      setResult('Error: ' + error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-900 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-2xl font-bold text-white mb-8">Admin Login Test</h1>
        
        <div className="space-y-4 mb-8">
          <Button
            onClick={testLogin}
            disabled={loading}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {loading ? 'Testing...' : 'Test Login'}
          </Button>
          
          <Button
            onClick={testVerify}
            disabled={loading}
            className="bg-green-600 hover:bg-green-700"
          >
            {loading ? 'Testing...' : 'Test Verify'}
          </Button>
          
          <Button
            onClick={clearSession}
            disabled={loading}
            className="bg-red-600 hover:bg-red-700"
          >
            {loading ? 'Clearing...' : 'Clear Session'}
          </Button>
        </div>
        
        {result && (
          <div className="bg-gray-800 border border-gray-700 rounded-lg p-4">
            <h2 className="text-white font-medium mb-2">Result:</h2>
            <pre className="text-gray-300 text-sm overflow-auto">
              {result}
            </pre>
          </div>
        )}
      </div>
    </div>
  )
}
