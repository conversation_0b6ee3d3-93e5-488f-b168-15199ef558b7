import { NextRequest, NextResponse } from 'next/server'
import { sendWelcomeEmail } from '@/lib/email'
import { requireAdmin } from '@/lib/auth-utils'

export async function POST(request: NextRequest) {
  try {
    // Only admins can send welcome emails
    await requireAdmin()
    
    const { email, firstName } = await request.json()
    
    if (!email || !firstName) {
      return NextResponse.json({
        success: false,
        message: '<PERSON><PERSON> and first<PERSON><PERSON> are required'
      }, { status: 400 })
    }
    
    await sendWelcomeEmail(email, {
      firstName,
      email,
      loginUrl: `${process.env.SITE_URL}/auth/signin`
    })
    
    return NextResponse.json({
      success: true,
      message: 'Welcome email sent successfully'
    })
    
  } catch (error: any) {
    console.error('Welcome email error:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to send welcome email'
    }, { status: 500 })
  }
}
