import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const categories = await prisma.category.findMany({
      orderBy: {
        sortOrder: 'asc'
      },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: categories
    })

  } catch (error: any) {
    console.error('Error fetching categories:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch categories'
    }, { status: 500 })
  }
}

export async function POST(request: NextRequest) {
  try {
    await requireAdmin()

    const { name, slug, description, sortOrder } = await request.json()

    if (!name || !slug) {
      return NextResponse.json({
        success: false,
        message: 'Name and slug are required'
      }, { status: 400 })
    }

    // Check if slug already exists
    const existingCategory = await prisma.category.findUnique({
      where: { slug }
    })

    if (existingCategory) {
      return NextResponse.json({
        success: false,
        message: 'A category with this slug already exists'
      }, { status: 400 })
    }

    const category = await prisma.category.create({
      data: {
        name,
        slug,
        description: description || null,
        sortOrder: sortOrder || 0
      },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: category
    })

  } catch (error: any) {
    console.error('Error creating category:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to create category'
    }, { status: 500 })
  }
}
