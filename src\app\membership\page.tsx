import { <PERSON>ada<PERSON> } from 'next'
import { <PERSON><PERSON> } from '@/components/ui/button'
import {
  CheckIcon,
  XMarkIcon,
  StarIcon,
  TrophyIcon,
  SparklesIcon
} from '@heroicons/react/24/outline'

export const metadata: Metadata = {
  title: 'Membership Plans - Forex Bot Zone',
  description: 'Choose the perfect membership plan for your trading needs. Get access to premium Forex bots and indicators at special member prices.',
}

const plans = [
  {
    name: 'Basic',
    price: 29,
    period: 'month',
    description: 'Perfect for beginners getting started with automated trading',
    icon: StarIcon,
    features: [
      'Access to 50+ Expert Advisors',
      'Basic indicators collection',
      'Email support',
      'Installation guides',
      'Basic trading signals',
      '30-day money back guarantee'
    ],
    notIncluded: [
      'Premium EAs',
      'Advanced indicators',
      'Priority support',
      'Custom development',
      'VIP trading signals'
    ],
    popular: false,
    buttonText: 'Start Basic Plan',
    buttonVariant: 'ghost' as const
  },
  {
    name: 'Professional',
    price: 79,
    period: 'month',
    description: 'Most popular choice for serious traders',
    icon: TrophyIcon,
    features: [
      'Access to 200+ Expert Advisors',
      'Full indicators library',
      'Priority email & chat support',
      'Video installation tutorials',
      'Advanced trading signals',
      'Monthly strategy webinars',
      'Risk management tools',
      '60-day money back guarantee'
    ],
    notIncluded: [
      'Custom EA development',
      'One-on-one coaching',
      'VIP trading signals'
    ],
    popular: true,
    buttonText: 'Start Professional Plan',
    buttonVariant: 'premium' as const
  },
  {
    name: 'VIP',
    price: 199,
    period: 'month',
    description: 'Ultimate package for professional traders',
    icon: SparklesIcon,
    features: [
      'Access to ALL Expert Advisors',
      'Complete indicators collection',
      '24/7 priority support',
      'Personal account manager',
      'VIP trading signals',
      'Weekly 1-on-1 coaching calls',
      'Custom EA development (1 per month)',
      'Exclusive VIP community access',
      'Early access to new products',
      '90-day money back guarantee'
    ],
    notIncluded: [],
    popular: false,
    buttonText: 'Start VIP Plan',
    buttonVariant: 'premium' as const
  }
]

const faqs = [
  {
    question: 'Can I cancel my membership anytime?',
    answer: 'Yes, you can cancel your membership at any time. Your access will continue until the end of your current billing period.'
  },
  {
    question: 'Do I keep access to downloaded products after canceling?',
    answer: 'Yes, any products you\'ve downloaded during your membership remain yours to keep, even after canceling.'
  },
  {
    question: 'Is there a setup fee?',
    answer: 'No, there are no setup fees or hidden charges. You only pay the monthly membership fee.'
  },
  {
    question: 'Can I upgrade or downgrade my plan?',
    answer: 'Yes, you can change your plan at any time. Changes take effect at your next billing cycle.'
  }
]

export default function MembershipPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Choose Your Trading Plan
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Get unlimited access to premium Forex Expert Advisors and Indicators. 
            Choose the plan that fits your trading style and budget.
          </p>
        </div>

        {/* Pricing Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-16">
          {plans.map((plan, index) => (
            <div
              key={plan.name}
              className={`relative bg-white/5 backdrop-blur-md border rounded-2xl p-8 ${
                plan.popular 
                  ? 'border-yellow-500/50 shadow-glow' 
                  : 'border-white/10 hover:border-yellow-500/30'
              } transition-all duration-300`}
            >
              {plan.popular && (
                <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                  <span className="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-4 py-1 rounded-full text-sm font-semibold">
                    Most Popular
                  </span>
                </div>
              )}

              <div className="text-center mb-8">
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                  <plan.icon className="w-8 h-8 text-black" />
                </div>
                <h3 className="text-2xl font-bold text-white mb-2">{plan.name}</h3>
                <p className="text-gray-400 mb-4">{plan.description}</p>
                <div className="flex items-baseline justify-center">
                  <span className="text-4xl font-bold text-white">${plan.price}</span>
                  <span className="text-gray-400 ml-1">/{plan.period}</span>
                </div>
              </div>

              <div className="space-y-4 mb-8">
                {plan.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-start">
                    <CheckIcon className="w-5 h-5 text-green-400 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-300 text-sm">{feature}</span>
                  </div>
                ))}
                {plan.notIncluded.map((feature, featureIndex) => (
                  <div key={featureIndex} className="flex items-start opacity-50">
                    <XMarkIcon className="w-5 h-5 text-gray-500 mr-3 mt-0.5 flex-shrink-0" />
                    <span className="text-gray-500 text-sm line-through">{feature}</span>
                  </div>
                ))}
              </div>

              <Button
                variant={plan.buttonVariant}
                size="lg"
                className="w-full"
              >
                {plan.buttonText}
              </Button>
            </div>
          ))}
        </div>

        {/* Features Comparison */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">
              Compare Plans
            </h2>
            <p className="text-gray-300">
              See what's included in each membership tier
            </p>
          </div>

          <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-2xl p-8">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-700">
                    <th className="text-left py-4 text-white font-semibold">Features</th>
                    <th className="text-center py-4 text-white font-semibold">Basic</th>
                    <th className="text-center py-4 text-white font-semibold">Professional</th>
                    <th className="text-center py-4 text-white font-semibold">VIP</th>
                  </tr>
                </thead>
                <tbody className="text-sm">
                  <tr className="border-b border-gray-800">
                    <td className="py-3 text-gray-300">Expert Advisors Access</td>
                    <td className="text-center py-3 text-gray-400">50+</td>
                    <td className="text-center py-3 text-gray-400">200+</td>
                    <td className="text-center py-3 text-green-400">Unlimited</td>
                  </tr>
                  <tr className="border-b border-gray-800">
                    <td className="py-3 text-gray-300">Support Level</td>
                    <td className="text-center py-3 text-gray-400">Email</td>
                    <td className="text-center py-3 text-gray-400">Priority</td>
                    <td className="text-center py-3 text-green-400">24/7 VIP</td>
                  </tr>
                  <tr className="border-b border-gray-800">
                    <td className="py-3 text-gray-300">Custom Development</td>
                    <td className="text-center py-3"><XMarkIcon className="w-4 h-4 text-red-400 mx-auto" /></td>
                    <td className="text-center py-3"><XMarkIcon className="w-4 h-4 text-red-400 mx-auto" /></td>
                    <td className="text-center py-3"><CheckIcon className="w-4 h-4 text-green-400 mx-auto" /></td>
                  </tr>
                  <tr>
                    <td className="py-3 text-gray-300">Money Back Guarantee</td>
                    <td className="text-center py-3 text-gray-400">30 days</td>
                    <td className="text-center py-3 text-gray-400">60 days</td>
                    <td className="text-center py-3 text-green-400">90 days</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        {/* FAQ */}
        <div className="mb-16">
          <div className="text-center mb-8">
            <h2 className="text-3xl font-bold text-white mb-4">
              Frequently Asked Questions
            </h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {faqs.map((faq, index) => (
              <div key={index} className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
                <h3 className="text-lg font-semibold text-white mb-3">
                  {faq.question}
                </h3>
                <p className="text-gray-300 text-sm">
                  {faq.answer}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-2xl p-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Start Trading Like a Pro?
          </h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            Join thousands of successful traders who rely on our premium tools for consistent profits.
          </p>
          <Button variant="premium" size="lg">
            Start Your Free Trial
          </Button>
        </div>
      </div>
    </div>
  )
}
