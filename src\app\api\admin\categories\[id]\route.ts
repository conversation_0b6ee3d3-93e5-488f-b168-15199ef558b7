import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()

    const category = await prisma.category.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    })

    if (!category) {
      return NextResponse.json({
        success: false,
        message: 'Category not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: category
    })

  } catch (error: any) {
    console.error('Error fetching category:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch category'
    }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()

    const { name, slug, description, sortOrder } = await request.json()

    if (!name || !slug) {
      return NextResponse.json({
        success: false,
        message: 'Name and slug are required'
      }, { status: 400 })
    }

    // Check if slug already exists (excluding current category)
    const existingCategory = await prisma.category.findFirst({
      where: {
        slug,
        NOT: {
          id: params.id
        }
      }
    })

    if (existingCategory) {
      return NextResponse.json({
        success: false,
        message: 'A category with this slug already exists'
      }, { status: 400 })
    }

    const category = await prisma.category.update({
      where: { id: params.id },
      data: {
        name,
        slug,
        description: description || null,
        sortOrder: sortOrder || 0
      },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    })

    return NextResponse.json({
      success: true,
      data: category
    })

  } catch (error: any) {
    console.error('Error updating category:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to update category'
    }, { status: 500 })
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()

    // Check if category has products
    const categoryWithProducts = await prisma.category.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: {
            products: true
          }
        }
      }
    })

    if (!categoryWithProducts) {
      return NextResponse.json({
        success: false,
        message: 'Category not found'
      }, { status: 404 })
    }

    if (categoryWithProducts._count.products > 0) {
      return NextResponse.json({
        success: false,
        message: `Cannot delete category with ${categoryWithProducts._count.products} products. Please move or delete the products first.`
      }, { status: 400 })
    }

    await prisma.category.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: 'Category deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting category:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to delete category'
    }, { status: 500 })
  }
}
