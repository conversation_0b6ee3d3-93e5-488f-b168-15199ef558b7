import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '30d'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (range) {
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setDate(now.getDate() - 30)
    }

    // Get analytics data
    const [
      totalViews,
      totalSales,
      totalRevenue,
      topProducts
    ] = await Promise.all([
      // <PERSON>ck views data - in real app, you'd track page views
      Promise.resolve(12543),
      
      prisma.order.count({
        where: {
          createdAt: {
            gte: startDate
          }
        }
      }),
      
      prisma.order.aggregate({
        where: {
          createdAt: {
            gte: startDate
          }
        },
        _sum: {
          totalAmount: true
        }
      }),
      
      prisma.orderItem.groupBy({
        by: ['productId'],
        _count: {
          productId: true
        },
        _sum: {
          price: true
        },
        orderBy: {
          _count: {
            productId: 'desc'
          }
        },
        take: 5
      })
    ])

    // Get product details for top products
    const topProductsWithDetails = await Promise.all(
      topProducts.map(async (item) => {
        const product = await prisma.product.findUnique({
          where: { id: item.productId },
          select: { name: true }
        })
        return {
          name: product?.name || 'Unknown Product',
          sales: item._count.productId,
          revenue: Number(item._sum.price || 0)
        }
      })
    )

    const conversionRate = totalViews > 0 ? (totalSales / totalViews) * 100 : 0

    const analytics = {
      totalViews,
      totalSales,
      totalRevenue: Number(totalRevenue._sum.totalAmount || 0),
      conversionRate: Math.round(conversionRate * 100) / 100,
      topProducts: topProductsWithDetails,
      salesTrend: [] // Mock data - in real app, you'd calculate daily/weekly trends
    }

    return NextResponse.json({
      success: true,
      data: analytics
    })

  } catch (error: any) {
    console.error('Error fetching analytics:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch analytics'
    }, { status: 500 })
  }
}
