import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { UserRole } from '@prisma/client'
import { prisma } from '@/lib/prisma'
import bcrypt from 'bcryptjs'
import jwt from 'jsonwebtoken'

export async function getCurrentUser() {
  const session = await getServerSession(authOptions)
  return session?.user
}

export async function requireAuth() {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('Authentication required')
  }
  return user
}

export async function requireAdmin() {
  const user = await requireAuth()
  if (user.role !== UserRole.ADMIN) {
    throw new Error('Admin access required')
  }
  return user
}

export async function hashPassword(password: string): Promise<string> {
  return bcrypt.hash(password, 12)
}

export async function verifyPassword(password: string, hashedPassword: string): Promise<boolean> {
  return bcrypt.compare(password, hashedPassword)
}

export function generateJWT(payload: any, expiresIn: string = '7d'): string {
  return jwt.sign(payload, process.env.JWT_SECRET!, { expiresIn })
}

export function verifyJWT(token: string): any {
  try {
    return jwt.verify(token, process.env.JWT_SECRET!)
  } catch (error) {
    throw new Error('Invalid token')
  }
}

export async function createUser(data: {
  email: string
  password: string
  firstName: string
  lastName: string
  role?: UserRole
}) {
  const existingUser = await prisma.user.findUnique({
    where: { email: data.email }
  })

  if (existingUser) {
    throw new Error('User already exists')
  }

  const hashedPassword = await hashPassword(data.password)

  return prisma.user.create({
    data: {
      email: data.email,
      password: hashedPassword,
      firstName: data.firstName,
      lastName: data.lastName,
      role: data.role || UserRole.CUSTOMER,
    },
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      role: true,
      createdAt: true,
    }
  })
}

export async function updateUserProfile(userId: string, data: {
  firstName?: string
  lastName?: string
  avatar?: string
}) {
  return prisma.user.update({
    where: { id: userId },
    data,
    select: {
      id: true,
      email: true,
      firstName: true,
      lastName: true,
      avatar: true,
      role: true,
    }
  })
}

export async function changePassword(userId: string, currentPassword: string, newPassword: string) {
  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: { password: true }
  })

  if (!user?.password) {
    throw new Error('User not found')
  }

  const isCurrentPasswordValid = await verifyPassword(currentPassword, user.password)
  if (!isCurrentPasswordValid) {
    throw new Error('Current password is incorrect')
  }

  const hashedNewPassword = await hashPassword(newPassword)

  return prisma.user.update({
    where: { id: userId },
    data: { password: hashedNewPassword },
    select: { id: true }
  })
}

export async function generatePasswordResetToken(email: string) {
  const user = await prisma.user.findUnique({
    where: { email }
  })

  if (!user) {
    throw new Error('User not found')
  }

  const token = generateJWT({ userId: user.id, email }, '1h')
  
  // Store token in database or cache for verification
  // For now, we'll just return the token
  return token
}

export async function resetPassword(token: string, newPassword: string) {
  try {
    const decoded = verifyJWT(token) as { userId: string; email: string }
    const hashedPassword = await hashPassword(newPassword)

    return prisma.user.update({
      where: { id: decoded.userId },
      data: { password: hashedPassword },
      select: { id: true, email: true }
    })
  } catch (error) {
    throw new Error('Invalid or expired token')
  }
}

export function isAdmin(user: any): boolean {
  return user?.role === UserRole.ADMIN
}

export function isCustomer(user: any): boolean {
  return user?.role === UserRole.CUSTOMER
}
