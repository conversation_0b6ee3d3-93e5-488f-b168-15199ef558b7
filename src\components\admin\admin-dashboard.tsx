'use client'

import { useEffect, useState } from 'react'
import { 
  UsersIcon,
  ShoppingBagIcon,
  CurrencyDollarIcon,
  ChartBarIcon,
  ArrowUpIcon,
  ArrowDownIcon
} from '@heroicons/react/24/outline'
import { DashboardStats } from '@/types'

interface StatCardProps {
  title: string
  value: string
  change: string
  changeType: 'increase' | 'decrease'
  icon: React.ComponentType<{ className?: string }>
}

function StatCard({ title, value, change, changeType, icon: Icon }: StatCardProps) {
  return (
    <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className="h-8 w-8 text-yellow-400" />
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-400 truncate">{title}</dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-white">{value}</div>
              <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                changeType === 'increase' ? 'text-green-400' : 'text-red-400'
              }`}>
                {changeType === 'increase' ? (
                  <ArrowUpIcon className="self-center flex-shrink-0 h-4 w-4" />
                ) : (
                  <ArrowDownIcon className="self-center flex-shrink-0 h-4 w-4" />
                )}
                <span className="sr-only">{changeType === 'increase' ? 'Increased' : 'Decreased'} by</span>
                {change}
              </div>
            </dd>
          </dl>
        </div>
      </div>
    </div>
  )
}

export function AdminDashboard() {
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setError(null)
        const response = await fetch('/api/admin/dashboard')
        if (response.ok) {
          const data = await response.json()
          setStats(data)
        } else {
          const errorData = await response.json().catch(() => ({}))
          setError(errorData.message || 'Failed to fetch dashboard stats')
        }
      } catch (error) {
        console.error('Error fetching dashboard stats:', error)
        setError('Failed to connect to server')
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="text-red-400 text-lg font-semibold mb-2">Error Loading Dashboard</div>
          <div className="text-gray-400 mb-4">{error}</div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    )
  }

  const statCards = [
    {
      title: 'Total Users',
      value: stats?.totalUsers.toLocaleString() || '0',
      change: '12%',
      changeType: 'increase' as const,
      icon: UsersIcon,
    },
    {
      title: 'Total Products',
      value: stats?.totalProducts.toLocaleString() || '0',
      change: '8%',
      changeType: 'increase' as const,
      icon: ShoppingBagIcon,
    },
    {
      title: 'Total Orders',
      value: stats?.totalOrders.toLocaleString() || '0',
      change: '15%',
      changeType: 'increase' as const,
      icon: ChartBarIcon,
    },
    {
      title: 'Total Revenue',
      value: `$${stats?.totalRevenue.toLocaleString() || '0'}`,
      change: '23%',
      changeType: 'increase' as const,
      icon: CurrencyDollarIcon,
    },
  ]

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div>
        <h1 className="text-3xl font-bold text-white">Dashboard</h1>
        <p className="mt-2 text-gray-400">
          Welcome to your admin dashboard. Here's what's happening with your store today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {statCards.map((card) => (
          <StatCard key={card.title} {...card} />
        ))}
      </div>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <h3 className="text-lg font-medium text-white mb-4">Recent Orders</h3>
          <div className="space-y-3">
            {stats?.recentOrders?.slice(0, 5).map((order) => (
              <div key={order.id} className="flex items-center justify-between py-2">
                <div>
                  <p className="text-sm font-medium text-white">Order #{order.id.slice(-8)}</p>
                  <p className="text-xs text-gray-400">{order.user.email}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-white">${order.totalAmount}</p>
                  <p className="text-xs text-gray-400">{order.status}</p>
                </div>
              </div>
            )) || (
              <p className="text-gray-400 text-sm">No recent orders</p>
            )}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <h3 className="text-lg font-medium text-white mb-4">Top Products</h3>
          <div className="space-y-3">
            {stats?.topProducts?.slice(0, 5).map((product) => (
              <div key={product.id} className="flex items-center justify-between py-2">
                <div>
                  <p className="text-sm font-medium text-white">{product.name}</p>
                  <p className="text-xs text-gray-400">${product.price}</p>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-white">{product.salesCount} sales</p>
                </div>
              </div>
            )) || (
              <p className="text-gray-400 text-sm">No product data</p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
