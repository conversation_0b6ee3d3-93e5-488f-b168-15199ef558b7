import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { exec, spawn } from 'child_process'
import { promisify } from 'util'
import path from 'path'
import fs from 'fs/promises'
import { prisma } from '@/lib/prisma'

const execAsync = promisify(exec)

// Fallback backup using Prisma queries
async function generatePrismaBackup(): Promise<string> {
  let sql = '-- Database Backup Generated by Prisma\n'
  sql += `-- Generated on: ${new Date().toISOString()}\n\n`

  try {
    // Get all table names from the database
    const tables = await prisma.$queryRaw<Array<{ tablename: string }>>`
      SELECT tablename FROM pg_tables WHERE schemaname = 'public'
    `

    for (const table of tables) {
      const tableName = table.tablename

      // Skip system tables
      if (tableName.startsWith('_') || tableName === 'spatial_ref_sys') {
        continue
      }

      sql += `-- Table: ${tableName}\n`

      try {
        // Get table data
        const rows = await prisma.$queryRawUnsafe(`SELECT * FROM "${tableName}"`)

        if (Array.isArray(rows) && rows.length > 0) {
          // Get column names from first row
          const columns = Object.keys(rows[0])

          sql += `DELETE FROM "${tableName}";\n`

          for (const row of rows) {
            const values = columns.map(col => {
              const value = row[col]
              if (value === null) return 'NULL'
              if (typeof value === 'string') return `'${value.replace(/'/g, "''")}'`
              if (value instanceof Date) return `'${value.toISOString()}'`
              if (typeof value === 'boolean') return value ? 'true' : 'false'
              if (typeof value === 'bigint') return value.toString()
              return value
            }).join(', ')

            sql += `INSERT INTO "${tableName}" (${columns.map(c => `"${c}"`).join(', ')}) VALUES (${values});\n`
          }
        }

        sql += '\n'
      } catch (tableError) {
        console.warn(`Failed to backup table ${tableName}:`, tableError.message)
        sql += `-- Error backing up table ${tableName}: ${tableError.message}\n\n`
      }
    }

    return sql
  } catch (error) {
    throw new Error(`Failed to generate Prisma backup: ${error.message}`)
  }
}

export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Create backup directory if it doesn't exist
    const backupDir = path.join(process.cwd(), 'Backup')
    try {
      await fs.access(backupDir)
    } catch {
      await fs.mkdir(backupDir, { recursive: true })
    }

    // Generate filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-').slice(0, 19)
    const filename = `backup_${timestamp}.sql`
    const backupPath = path.join(backupDir, filename)

    // Get database URL from environment
    const databaseUrl = process.env.DATABASE_URL
    if (!databaseUrl) {
      return NextResponse.json(
        { message: 'Database URL not configured' },
        { status: 500 }
      )
    }

    // Parse database URL to extract connection details
    const url = new URL(databaseUrl)
    const dbName = url.pathname.slice(1) // Remove leading slash
    const host = url.hostname
    const port = url.port || '5432'
    const username = url.username
    const password = url.password

    // Try different approaches for backup
    try {
      let backupSuccess = false
      let backupData = ''

      // Method 1: Try using pg_dump with proper Windows command
      try {
        const isWindows = process.platform === 'win32'
        let command: string

        if (isWindows) {
          // Windows command with proper escaping
          command = `set PGPASSWORD=${password} && pg_dump -h ${host} -p ${port} -U ${username} -d ${dbName} --no-owner --no-privileges --clean --if-exists`
        } else {
          // Unix/Linux command
          command = `PGPASSWORD="${password}" pg_dump -h ${host} -p ${port} -U ${username} -d ${dbName} --no-owner --no-privileges --clean --if-exists`
        }

        const { stdout } = await execAsync(command, { maxBuffer: 1024 * 1024 * 10 }) // 10MB buffer
        backupData = stdout
        backupSuccess = true
      } catch (pgDumpError) {
        console.log('pg_dump failed, trying alternative method:', pgDumpError.message)

        // Method 2: Use Prisma to generate SQL backup
        try {
          backupData = await generatePrismaBackup()
          backupSuccess = true
        } catch (prismaError) {
          console.error('Prisma backup also failed:', prismaError.message)
          throw new Error(`Both pg_dump and Prisma backup failed. pg_dump error: ${pgDumpError.message}`)
        }
      }

      if (backupSuccess && backupData) {
        // Write backup data to file
        await fs.writeFile(backupPath, backupData, 'utf8')

        // Verify backup file was created
        const stats = await fs.stat(backupPath)
        if (stats.size === 0) {
          throw new Error('Backup file is empty')
        }

        return NextResponse.json({
          message: 'Database backup created successfully',
          filename,
          size: stats.size,
          path: backupPath
        })
      } else {
        throw new Error('Failed to generate backup data')
      }
    } catch (error) {
      console.error('Backup error:', error)

      // Clean up empty backup file if it exists
      try {
        await fs.unlink(backupPath)
      } catch {}

      return NextResponse.json(
        { message: 'Failed to create database backup', error: error.message },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error('Database backup error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
