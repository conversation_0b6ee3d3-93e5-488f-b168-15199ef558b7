import { PrismaClient, EmailTemplateType } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting email templates seed...')

  // Welcome Email Template
  const welcomeTemplate = await prisma.emailTemplate.upsert({
    where: { id: 'welcome-template' },
    update: {},
    create: {
      id: 'welcome-template',
      name: 'Welcome Email',
      subject: 'Welcome to Forex Bot Zone - Your Trading Journey Starts Here!',
      type: EmailTemplateType.WELCOME,
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to Forex Bot Zone</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #1f2937 0%, #3b82f6 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: #fbbf24; margin: 0; font-size: 28px;">Welcome to Forex Bot Zone!</h1>
          </div>
          
          <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #1f2937; margin-top: 0;">Hello {{firstName}}!</h2>
            
            <p>Thank you for joining <strong>Forex Bot Zone</strong>, your premier destination for professional forex trading tools and expert advisors.</p>
            
            <p>Your account has been successfully created with the email: <strong>{{email}}</strong></p>
            
            <div style="background: #e0f2fe; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #0277bd; margin-top: 0;">What's Next?</h3>
              <ul style="color: #01579b; margin: 0; padding-left: 20px;">
                <li>Browse our premium collection of Expert Advisors</li>
                <li>Explore professional trading indicators</li>
                <li>Access exclusive trading strategies</li>
                <li>Join our community of successful traders</li>
              </ul>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="{{loginUrl}}" style="background: #fbbf24; color: #1f2937; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                Access Your Account
              </a>
            </div>
            
            <p style="color: #6b7280; font-size: 14px; margin-top: 30px;">
              If you have any questions, feel free to contact our support team.
            </p>
            
            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
            
            <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">
              © 2024 Forex Bot Zone. All rights reserved.
            </p>
          </div>
        </body>
        </html>
      `,
      textContent: `
        Welcome to Forex Bot Zone!
        
        Hello {{firstName}}!
        
        Thank you for joining Forex Bot Zone, your premier destination for professional forex trading tools and expert advisors.
        
        Your account has been successfully created with the email: {{email}}
        
        What's Next?
        - Browse our premium collection of Expert Advisors
        - Explore professional trading indicators
        - Access exclusive trading strategies
        - Join our community of successful traders
        
        Access your account: {{loginUrl}}
        
        If you have any questions, feel free to contact our support team.
        
        © 2024 Forex Bot Zone. All rights reserved.
      `,
      variables: {
        firstName: 'User\'s first name',
        email: 'User\'s email address',
        loginUrl: 'Login page URL'
      }
    }
  })

  // Password Reset Template
  const passwordResetTemplate = await prisma.emailTemplate.upsert({
    where: { id: 'password-reset-template' },
    update: {},
    create: {
      id: 'password-reset-template',
      name: 'Password Reset',
      subject: 'Reset Your Password - Forex Bot Zone',
      type: EmailTemplateType.PASSWORD_RESET,
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Reset Your Password - Forex Bot Zone</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #1f2937 0%, #ef4444 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: #fff; margin: 0; font-size: 28px;">Password Reset</h1>
          </div>
          
          <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #1f2937; margin-top: 0;">Hello {{firstName}}!</h2>
            
            <p>We received a request to reset your password for your Forex Bot Zone account.</p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="{{resetUrl}}" style="background: #ef4444; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                Reset Your Password
              </a>
            </div>
            
            <div style="background: #fef2f2; border: 1px solid #fecaca; padding: 15px; border-radius: 6px; margin: 20px 0;">
              <p style="color: #dc2626; margin: 0; font-size: 14px;">
                <strong>Important:</strong> This link will expire in {{expiresIn}}. If you didn't request this password reset, please ignore this email.
              </p>
            </div>
            
            <p style="color: #6b7280; font-size: 14px;">
              For security reasons, this link can only be used once and will expire automatically.
            </p>
            
            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
            
            <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">
              © 2024 Forex Bot Zone. All rights reserved.
            </p>
          </div>
        </body>
        </html>
      `,
      textContent: `
        Password Reset - Forex Bot Zone
        
        Hello {{firstName}}!
        
        We received a request to reset your password for your Forex Bot Zone account.
        
        Reset your password: {{resetUrl}}
        
        Important: This link will expire in {{expiresIn}}. If you didn't request this password reset, please ignore this email.
        
        For security reasons, this link can only be used once and will expire automatically.
        
        © 2024 Forex Bot Zone. All rights reserved.
      `,
      variables: {
        firstName: 'User\'s first name',
        resetUrl: 'Password reset URL with token',
        expiresIn: 'Expiration time (e.g., 24 hours)'
      }
    }
  })

  // Order Confirmation Template
  const orderConfirmationTemplate = await prisma.emailTemplate.upsert({
    where: { id: 'order-confirmation-template' },
    update: {},
    create: {
      id: 'order-confirmation-template',
      name: 'Order Confirmation',
      subject: 'Order Confirmation - Your Purchase is Complete!',
      type: EmailTemplateType.ORDER_CONFIRMATION,
      htmlContent: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Order Confirmation - Forex Bot Zone</title>
        </head>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #1f2937 0%, #10b981 100%); padding: 30px; text-align: center; border-radius: 10px 10px 0 0;">
            <h1 style="color: #fff; margin: 0; font-size: 28px;">Order Confirmed!</h1>
          </div>
          
          <div style="background: #f8fafc; padding: 30px; border-radius: 0 0 10px 10px;">
            <h2 style="color: #1f2937; margin-top: 0;">Thank you, {{customerName}}!</h2>
            
            <p>Your order has been successfully processed and is ready for download.</p>
            
            <div style="background: #fff; border: 1px solid #e5e7eb; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #1f2937; margin-top: 0;">Order Details</h3>
              <p><strong>Order Number:</strong> {{orderNumber}}</p>
              <p><strong>Total:</strong> $\{{total}}</p>
            </div>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="{{downloadUrl}}" style="background: #10b981; color: #fff; padding: 12px 30px; text-decoration: none; border-radius: 6px; font-weight: bold; display: inline-block;">
                Download Your Files
              </a>
            </div>
            
            <p style="color: #6b7280; font-size: 14px;">
              Your download link will remain active for the duration specified in your purchase terms.
            </p>
            
            <hr style="border: none; border-top: 1px solid #e5e7eb; margin: 30px 0;">
            
            <p style="color: #9ca3af; font-size: 12px; text-align: center; margin: 0;">
              © 2024 Forex Bot Zone. All rights reserved.
            </p>
          </div>
        </body>
        </html>
      `,
      textContent: `
        Order Confirmation - Forex Bot Zone
        
        Thank you, {{customerName}}!
        
        Your order has been successfully processed and is ready for download.
        
        Order Details:
        Order Number: {{orderNumber}}
        Total: $\{{total}}
        
        Download your files: {{downloadUrl}}
        
        Your download link will remain active for the duration specified in your purchase terms.
        
        © 2024 Forex Bot Zone. All rights reserved.
      `,
      variables: {
        customerName: 'Customer\'s name',
        orderNumber: 'Order number',
        total: 'Order total amount',
        downloadUrl: 'Download page URL'
      }
    }
  })

  console.log('✅ Welcome email template created')
  console.log('✅ Password reset email template created')
  console.log('✅ Order confirmation email template created')
  console.log('🎉 Email templates seed completed successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Email templates seed failed:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
