import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'
import { deleteFile } from '@/lib/cloudflare-r2'
import { z } from 'zod'

const addFileSchema = z.object({
  fileName: z.string().min(1, 'File name is required'),
  fileUrl: z.string().url('Valid file URL is required'),
  fileSize: z.number().min(1, 'File size is required'),
  fileType: z.string().min(1, 'File type is required'),
})

// GET /api/products/[id]/files - Get product files
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin() // Only admins can view file details
    
    const files = await prisma.productFile.findMany({
      where: { productId: params.id },
      orderBy: { createdAt: 'desc' }
    })

    // Convert BigInt to string for JSON serialization
    const serializedFiles = files.map(file => ({
      ...file,
      fileSize: file.fileSize.toString()
    }))

    return NextResponse.json({
      success: true,
      data: serializedFiles
    })

  } catch (error: any) {
    console.error('Error fetching product files:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch product files'
    }, { status: 500 })
  }
}

// POST /api/products/[id]/files - Add file to product
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const validatedData = addFileSchema.parse(body)

    // Verify product exists
    const product = await prisma.product.findUnique({
      where: { id: params.id }
    })

    if (!product) {
      return NextResponse.json({
        success: false,
        message: 'Product not found'
      }, { status: 404 })
    }

    const productFile = await prisma.productFile.create({
      data: {
        ...validatedData,
        productId: params.id,
        fileSize: BigInt(validatedData.fileSize)
      }
    })

    // Convert BigInt to string for JSON serialization
    const serializedFile = {
      ...productFile,
      fileSize: productFile.fileSize.toString()
    }

    return NextResponse.json({
      success: true,
      message: 'File added to product successfully',
      data: serializedFile
    }, { status: 201 })

  } catch (error: any) {
    console.error('Error adding file to product:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to add file to product'
    }, { status: 500 })
  }
}

// DELETE /api/products/[id]/files/[fileId] - Remove file from product
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()
    
    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('fileId')

    if (!fileId) {
      return NextResponse.json({
        success: false,
        message: 'File ID is required'
      }, { status: 400 })
    }

    // Get file details
    const productFile = await prisma.productFile.findUnique({
      where: { 
        id: fileId,
        productId: params.id
      }
    })

    if (!productFile) {
      return NextResponse.json({
        success: false,
        message: 'File not found'
      }, { status: 404 })
    }

    // Delete from R2
    try {
      // Extract key from URL
      const url = new URL(productFile.fileUrl)
      const key = url.pathname.substring(1) // Remove leading slash
      await deleteFile(key)
    } catch (error) {
      console.error('Error deleting file from R2:', error)
      // Continue with database deletion even if R2 deletion fails
    }

    // Delete from database
    await prisma.productFile.delete({
      where: { id: fileId }
    })

    return NextResponse.json({
      success: true,
      message: 'File removed from product successfully'
    })

  } catch (error: any) {
    console.error('Error removing file from product:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to remove file from product'
    }, { status: 500 })
  }
}
