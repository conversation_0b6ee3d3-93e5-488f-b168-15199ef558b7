import { Metadata } from 'next'
import { 
  TrophyIcon,
  UserGroupIcon,
  ShieldCheckIcon,
  StarIcon,
  ChartBarIcon,
  CogIcon
} from '@heroicons/react/24/outline'

export const metadata: Metadata = {
  title: 'About Us - Forex Bot Zone',
  description: 'Learn about Forex Bot Zone - your trusted source for premium Forex Expert Advisors and Indicators at special prices.',
}

const stats = [
  { name: 'Happy Customers', value: '10,000+' },
  { name: 'Premium Products', value: '500+' },
  { name: 'Success Rate', value: '95%' },
  { name: 'Years Experience', value: '8+' },
]

const features = [
  {
    icon: TrophyIcon,
    title: 'Premium Quality',
    description: 'All our trading bots and indicators are thoroughly tested and verified by professional traders before release.'
  },
  {
    icon: UserGroupIcon,
    title: 'Expert Team',
    description: 'Our team consists of experienced traders, developers, and market analysts with decades of combined experience.'
  },
  {
    icon: ShieldCheckIcon,
    title: 'Secure & Reliable',
    description: 'Enterprise-grade security, reliable downloads, and lifetime access to your purchased products.'
  },
  {
    icon: StarIcon,
    title: 'Special Pricing',
    description: 'Get premium trading tools at unbeatable special prices - up to 90% off regular market rates.'
  },
  {
    icon: ChartBarIcon,
    title: 'Proven Results',
    description: 'Our products are backed by real trading results and testimonials from successful traders worldwide.'
  },
  {
    icon: CogIcon,
    title: 'Easy Setup',
    description: 'Simple installation guides and 24/7 support to get you trading with our tools in minutes.'
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Hero Section */}
        <div className="text-center mb-16">
          <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-2xl flex items-center justify-center mx-auto mb-6">
            <span className="text-black font-bold text-2xl">FBZ</span>
          </div>
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
            About Forex Bot Zone
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Your trusted source for premium Forex Expert Advisors and Indicators at special prices. 
            We're dedicated to providing professional traders with the tools they need to succeed.
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 mb-16">
          {stats.map((stat) => (
            <div key={stat.name} className="text-center">
              <div className="text-3xl md:text-4xl font-bold text-yellow-400 mb-2">
                {stat.value}
              </div>
              <div className="text-gray-300 font-medium">
                {stat.name}
              </div>
            </div>
          ))}
        </div>

        {/* Story Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="text-3xl font-bold text-white mb-6">Our Story</h2>
            <div className="space-y-4 text-gray-300">
              <p>
                Founded in 2016, Forex Bot Zone started with a simple mission: make premium trading tools 
                accessible to every trader, regardless of their budget. We noticed that high-quality Expert 
                Advisors and Indicators were often priced out of reach for many aspiring traders.
              </p>
              <p>
                Our team of experienced traders and developers decided to change that. We began creating 
                and curating the best trading tools available, then offering them at special prices that 
                make professional trading accessible to everyone.
              </p>
              <p>
                Today, we're proud to serve over 10,000 traders worldwide, providing them with the tools 
                and support they need to achieve consistent profitability in the Forex markets.
              </p>
            </div>
          </div>
          <div>
            <h2 className="text-3xl font-bold text-white mb-6">Our Mission</h2>
            <div className="space-y-4 text-gray-300">
              <p>
                To democratize access to professional trading tools by offering premium Expert Advisors 
                and Indicators at special prices that every trader can afford.
              </p>
              <p>
                We believe that success in trading shouldn't be limited by budget constraints. Every trader 
                deserves access to the same high-quality tools used by professionals and institutions.
              </p>
              <p>
                Our commitment extends beyond just selling products - we provide comprehensive support, 
                detailed documentation, and a community where traders can learn and grow together.
              </p>
            </div>
          </div>
        </div>

        {/* Features Grid */}
        <div className="mb-16">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-white mb-4">
              Why Choose Forex Bot Zone?
            </h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We're more than just a marketplace - we're your partners in trading success.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div
                key={index}
                className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6 hover:border-yellow-500/30 hover:shadow-glow transition-all duration-300"
              >
                <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-lg flex items-center justify-center mb-4">
                  <feature.icon className="w-6 h-6 text-black" />
                </div>
                <h3 className="text-xl font-semibold text-white mb-3">
                  {feature.title}
                </h3>
                <p className="text-gray-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-2xl p-8">
          <h2 className="text-3xl font-bold text-white mb-4">
            Ready to Start Trading Like a Pro?
          </h2>
          <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
            Join thousands of successful traders who trust Forex Bot Zone for their trading tools. 
            Browse our collection of premium Expert Advisors and Indicators at special prices.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a
              href="/products"
              className="inline-flex items-center justify-center px-6 py-3 bg-gradient-to-r from-yellow-400 to-orange-500 text-black font-semibold rounded-lg hover:from-yellow-500 hover:to-orange-600 transition-all duration-200"
            >
              Browse Products
            </a>
            <a
              href="/contact"
              className="inline-flex items-center justify-center px-6 py-3 border border-yellow-500/30 text-yellow-400 font-semibold rounded-lg hover:bg-yellow-500/10 transition-all duration-200"
            >
              Contact Us
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
