/**
 * Debug utility for development and troubleshooting
 */

export interface DebugInfo {
  timestamp: string
  level: 'info' | 'warn' | 'error' | 'debug'
  category: string
  message: string
  data?: any
  stack?: string
}

class DebugLogger {
  private logs: DebugInfo[] = []
  private maxLogs = 1000
  private isEnabled = process.env.NODE_ENV === 'development'

  /**
   * Log a debug message
   */
  log(level: DebugInfo['level'], category: string, message: string, data?: any) {
    if (!this.isEnabled) return

    const debugInfo: DebugInfo = {
      timestamp: new Date().toISOString(),
      level,
      category,
      message,
      data,
      stack: level === 'error' ? new Error().stack : undefined
    }

    this.logs.push(debugInfo)

    // Keep only the last maxLogs entries
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(-this.maxLogs)
    }

    // Console output with colors
    const colors = {
      info: '\x1b[36m',    // <PERSON>an
      warn: '\x1b[33m',    // Yellow
      error: '\x1b[31m',   // Red
      debug: '\x1b[35m',   // Magenta
    }
    const reset = '\x1b[0m'

    console.log(
      `${colors[level]}[${level.toUpperCase()}]${reset} ${category}: ${message}`,
      data ? data : ''
    )
  }

  info(category: string, message: string, data?: any) {
    this.log('info', category, message, data)
  }

  warn(category: string, message: string, data?: any) {
    this.log('warn', category, message, data)
  }

  error(category: string, message: string, data?: any) {
    this.log('error', category, message, data)
  }

  debug(category: string, message: string, data?: any) {
    this.log('debug', category, message, data)
  }

  /**
   * Get all logs
   */
  getLogs(): DebugInfo[] {
    return [...this.logs]
  }

  /**
   * Get logs by category
   */
  getLogsByCategory(category: string): DebugInfo[] {
    return this.logs.filter(log => log.category === category)
  }

  /**
   * Get logs by level
   */
  getLogsByLevel(level: DebugInfo['level']): DebugInfo[] {
    return this.logs.filter(log => log.level === level)
  }

  /**
   * Clear all logs
   */
  clear() {
    this.logs = []
  }

  /**
   * Export logs as JSON
   */
  export(): string {
    return JSON.stringify(this.logs, null, 2)
  }

  /**
   * Get system information
   */
  getSystemInfo() {
    const info = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      platform: typeof window !== 'undefined' ? 'client' : 'server',
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'N/A',
      url: typeof window !== 'undefined' ? window.location.href : 'N/A',
      memory: typeof process !== 'undefined' ? process.memoryUsage() : 'N/A',
      version: process.env.npm_package_version || 'unknown',
    }

    this.info('SYSTEM', 'System information collected', info)
    return info
  }
}

// Create singleton instance
export const debugLogger = new DebugLogger()

// Convenience functions
export const debug = {
  info: (category: string, message: string, data?: any) => 
    debugLogger.info(category, message, data),
  
  warn: (category: string, message: string, data?: any) => 
    debugLogger.warn(category, message, data),
  
  error: (category: string, message: string, data?: any) => 
    debugLogger.error(category, message, data),
  
  debug: (category: string, message: string, data?: any) => 
    debugLogger.debug(category, message, data),
  
  auth: (message: string, data?: any) => 
    debugLogger.info('AUTH', message, data),
  
  api: (message: string, data?: any) => 
    debugLogger.info('API', message, data),
  
  db: (message: string, data?: any) => 
    debugLogger.info('DATABASE', message, data),
  
  email: (message: string, data?: any) => 
    debugLogger.info('EMAIL', message, data),
  
  payment: (message: string, data?: any) => 
    debugLogger.info('PAYMENT', message, data),
  
  download: (message: string, data?: any) => 
    debugLogger.info('DOWNLOAD', message, data),
}

/**
 * Performance monitoring
 */
export class PerformanceMonitor {
  private timers: Map<string, number> = new Map()

  start(label: string) {
    this.timers.set(label, Date.now())
    debug.debug('PERFORMANCE', `Started timer: ${label}`)
  }

  end(label: string): number {
    const startTime = this.timers.get(label)
    if (!startTime) {
      debug.warn('PERFORMANCE', `Timer not found: ${label}`)
      return 0
    }

    const duration = Date.now() - startTime
    this.timers.delete(label)
    
    debug.info('PERFORMANCE', `${label} completed in ${duration}ms`)
    return duration
  }

  measure<T>(label: string, fn: () => T): T {
    this.start(label)
    try {
      const result = fn()
      this.end(label)
      return result
    } catch (error) {
      this.end(label)
      throw error
    }
  }

  async measureAsync<T>(label: string, fn: () => Promise<T>): Promise<T> {
    this.start(label)
    try {
      const result = await fn()
      this.end(label)
      return result
    } catch (error) {
      this.end(label)
      throw error
    }
  }
}

export const perf = new PerformanceMonitor()

/**
 * Environment checker
 */
export function checkEnvironment() {
  const requiredVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL',
  ]

  const optionalVars = [
    'RESEND_API_KEY',
    'CLOUDFLARE_R2_ACCESS_KEY_ID',
    'CLOUDFLARE_R2_SECRET_ACCESS_KEY',
    'CLOUDFLARE_R2_BUCKET_NAME',
  ]

  const missing = requiredVars.filter(varName => !process.env[varName])
  const missingOptional = optionalVars.filter(varName => !process.env[varName])

  const status = {
    isValid: missing.length === 0,
    missing,
    missingOptional,
    present: requiredVars.filter(varName => !!process.env[varName]),
    presentOptional: optionalVars.filter(varName => !!process.env[varName]),
  }

  debug.info('ENVIRONMENT', 'Environment check completed', status)
  
  if (missing.length > 0) {
    debug.error('ENVIRONMENT', `Missing required variables: ${missing.join(', ')}`)
  }

  return status
}

/**
 * Database connection checker
 */
export async function checkDatabase() {
  try {
    const { prisma } = await import('@/lib/prisma')
    await prisma.$queryRaw`SELECT 1`
    debug.info('DATABASE', 'Database connection successful')
    return { connected: true, error: null }
  } catch (error) {
    debug.error('DATABASE', 'Database connection failed', error)
    return { connected: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

/**
 * Health check function
 */
export async function healthCheck() {
  const health = {
    timestamp: new Date().toISOString(),
    status: 'healthy' as 'healthy' | 'unhealthy',
    checks: {
      environment: checkEnvironment(),
      database: await checkDatabase(),
    },
    system: debugLogger.getSystemInfo(),
  }

  if (!health.checks.environment.isValid || !health.checks.database.connected) {
    health.status = 'unhealthy'
  }

  debug.info('HEALTH', 'Health check completed', health)
  return health
}
