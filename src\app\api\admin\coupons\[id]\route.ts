import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateCouponSchema = z.object({
  code: z.string().min(1, 'Coupon code is required').max(50).optional(),
  name: z.string().min(1, 'Coupon name is required').max(100).optional(),
  description: z.string().optional(),
  type: z.enum(['FIXED_AMOUNT', 'PERCENTAGE', 'FREE_SHIPPING']).optional(),
  value: z.number().min(0, 'Value must be positive').optional(),
  minimumAmount: z.number().min(0).optional(),
  maximumDiscount: z.number().min(0).optional(),
  usageLimit: z.number().min(1).optional(),
  userUsageLimit: z.number().min(1).optional(),
  isActive: z.boolean().optional(),
  startsAt: z.string().datetime().optional(),
  expiresAt: z.string().datetime().optional()
})

// GET - Get single coupon
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const coupon = await prisma.coupon.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { couponUsages: true }
        },
        couponUsages: {
          include: {
            user: {
              select: { email: true, firstName: true, lastName: true }
            },
            order: {
              select: { orderNumber: true, totalAmount: true }
            }
          },
          orderBy: { usedAt: 'desc' },
          take: 10
        }
      }
    })

    if (!coupon) {
      return NextResponse.json({
        success: false,
        message: 'Coupon not found'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: coupon
    })

  } catch (error: any) {
    console.error('Error fetching coupon:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch coupon'
    }, { status: 500 })
  }
}

// PUT - Update coupon
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const data = updateCouponSchema.parse(body)

    // Check if coupon exists
    const existingCoupon = await prisma.coupon.findUnique({
      where: { id: params.id }
    })

    if (!existingCoupon) {
      return NextResponse.json({
        success: false,
        message: 'Coupon not found'
      }, { status: 404 })
    }

    const updateData: any = { ...data }

    // Convert code to uppercase if provided
    if (data.code) {
      updateData.code = data.code.toUpperCase()
      
      // Check if new code conflicts with existing coupon
      if (updateData.code !== existingCoupon.code) {
        const codeExists = await prisma.coupon.findUnique({
          where: { code: updateData.code }
        })
        
        if (codeExists) {
          return NextResponse.json({
            success: false,
            message: 'Coupon code already exists'
          }, { status: 400 })
        }
      }
    }

    // Validate percentage coupons
    if (data.type === 'PERCENTAGE' && data.value && data.value > 100) {
      return NextResponse.json({
        success: false,
        message: 'Percentage value cannot exceed 100%'
      }, { status: 400 })
    }

    // Convert date strings to Date objects
    if (data.startsAt) {
      updateData.startsAt = new Date(data.startsAt)
    }
    if (data.expiresAt) {
      updateData.expiresAt = new Date(data.expiresAt)
    }

    const coupon = await prisma.coupon.update({
      where: { id: params.id },
      data: updateData
    })

    return NextResponse.json({
      success: true,
      data: coupon
    })

  } catch (error: any) {
    console.error('Error updating coupon:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to update coupon'
    }, { status: 500 })
  }
}

// DELETE - Delete coupon
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    // Check if coupon exists
    const existingCoupon = await prisma.coupon.findUnique({
      where: { id: params.id },
      include: {
        _count: {
          select: { couponUsages: true }
        }
      }
    })

    if (!existingCoupon) {
      return NextResponse.json({
        success: false,
        message: 'Coupon not found'
      }, { status: 404 })
    }

    // Check if coupon has been used
    if (existingCoupon._count.couponUsages > 0) {
      return NextResponse.json({
        success: false,
        message: 'Cannot delete coupon that has been used. Consider deactivating it instead.'
      }, { status: 400 })
    }

    await prisma.coupon.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: 'Coupon deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting coupon:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to delete coupon'
    }, { status: 500 })
  }
}
