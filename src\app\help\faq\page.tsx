'use client'

import { useState } from 'react'
import Link from 'next/link'
import { 
  ArrowLeftIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline'

export default function FAQPage() {
  const [openItems, setOpenItems] = useState<number[]>([])
  const [searchTerm, setSearchTerm] = useState('')

  const toggleItem = (index: number) => {
    setOpenItems(prev => 
      prev.includes(index) 
        ? prev.filter(i => i !== index)
        : [...prev, index]
    )
  }

  const faqCategories = [
    {
      category: 'General Questions',
      items: [
        {
          question: 'What types of trading products do you offer?',
          answer: 'We offer Expert Advisors (EAs), custom indicators, trading systems, and educational materials for MetaTrader 4 and 5 platforms. Our products cover various trading strategies including scalping, swing trading, and automated systems.'
        },
        {
          question: 'Are your Expert Advisors compatible with all brokers?',
          answer: 'Most of our EAs are compatible with major forex brokers that support MetaTrader 4 and 5. However, some EAs may have specific requirements regarding spread, execution type, or broker features. Check the product description for compatibility details.'
        },
        {
          question: 'Do you offer refunds?',
          answer: 'We offer a 30-day money-back guarantee on all our products. If you\'re not satisfied with your purchase, contact our support team within 30 days for a full refund.'
        },
        {
          question: 'How do I access my purchased products?',
          answer: 'After purchase, you can download your products from the "Downloads" section in your account dashboard. You\'ll also receive an email with download links.'
        }
      ]
    },
    {
      category: 'Installation & Setup',
      items: [
        {
          question: 'How do I install an Expert Advisor?',
          answer: 'Copy the EA file to your MetaTrader\'s MQL4/Experts or MQL5/Experts folder, restart the platform, and drag the EA onto your chart. Make sure to enable "Allow live trading" in the settings.'
        },
        {
          question: 'Why isn\'t my EA trading automatically?',
          answer: 'Check that: 1) "Allow live trading" is enabled in MT4/5 settings, 2) The EA is attached to the correct chart, 3) Market is open, 4) You have sufficient account balance, 5) The EA settings are configured correctly.'
        },
        {
          question: 'Can I run multiple EAs on the same account?',
          answer: 'Yes, you can run multiple EAs on different currency pairs or timeframes. However, ensure they don\'t conflict with each other and that you have sufficient margin for all positions.'
        },
        {
          question: 'What are the minimum system requirements?',
          answer: 'You need MetaTrader 4 or 5, Windows 7 or later (or Wine for Linux/Mac), at least 2GB RAM, and a stable internet connection. VPS hosting is recommended for 24/7 trading.'
        }
      ]
    },
    {
      category: 'Account & Billing',
      items: [
        {
          question: 'How do I create an account?',
          answer: 'Click "Sign Up" in the top menu, fill in your details, and verify your email address. You can then browse and purchase products immediately.'
        },
        {
          question: 'What payment methods do you accept?',
          answer: 'We accept all major credit cards, PayPal, and various digital payment methods through our secure payment processor Stripe.'
        },
        {
          question: 'Can I download products multiple times?',
          answer: 'Yes, you have unlimited downloads of your purchased products. Access them anytime from your account dashboard.'
        },
        {
          question: 'Do you offer volume discounts?',
          answer: 'Yes, we offer discounts for bulk purchases and have special pricing for educational institutions. Contact our sales team for custom quotes.'
        }
      ]
    },
    {
      category: 'Technical Support',
      items: [
        {
          question: 'My EA stopped working after a broker update. What should I do?',
          answer: 'First, check if there\'s an updated version of the EA in your downloads. If not, contact our support team with details about your broker and the issue you\'re experiencing.'
        },
        {
          question: 'How do I optimize EA settings for my account?',
          answer: 'Start with default settings and gradually adjust based on your risk tolerance, account size, and trading preferences. Use the strategy tester for backtesting before live trading.'
        },
        {
          question: 'Can you help me set up my EA?',
          answer: 'Yes, we offer installation support and basic setup guidance. For advanced customization, we also provide paid consultation services.'
        },
        {
          question: 'What if I find a bug in the software?',
          answer: 'Report any bugs to our support team with detailed information about the issue, your setup, and steps to reproduce the problem. We\'ll investigate and provide fixes promptly.'
        }
      ]
    }
  ]

  const filteredFAQs = faqCategories.map(category => ({
    ...category,
    items: category.items.filter(item => 
      item.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.answer.toLowerCase().includes(searchTerm.toLowerCase())
    )
  })).filter(category => category.items.length > 0)

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            href="/help" 
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors duration-200 mb-4"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Help Center
          </Link>
          <h1 className="text-4xl font-bold text-white mb-4">Frequently Asked Questions</h1>
          <p className="text-xl text-gray-300">
            Find quick answers to the most common questions about our products and services
          </p>
        </div>

        {/* Search */}
        <div className="mb-8">
          <div className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              placeholder="Search FAQ..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="trading-input pl-10 w-full"
            />
          </div>
        </div>

        {/* FAQ Categories */}
        <div className="space-y-8">
          {filteredFAQs.map((category, categoryIndex) => (
            <div key={categoryIndex}>
              <h2 className="text-2xl font-bold text-white mb-6">{category.category}</h2>
              <div className="space-y-4">
                {category.items.map((item, itemIndex) => {
                  const globalIndex = categoryIndex * 100 + itemIndex
                  const isOpen = openItems.includes(globalIndex)
                  
                  return (
                    <div 
                      key={itemIndex}
                      className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl overflow-hidden"
                    >
                      <button
                        onClick={() => toggleItem(globalIndex)}
                        className="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-800/50 transition-colors duration-200"
                      >
                        <h3 className="text-lg font-semibold text-white pr-4">{item.question}</h3>
                        {isOpen ? (
                          <ChevronUpIcon className="w-5 h-5 text-yellow-400 flex-shrink-0" />
                        ) : (
                          <ChevronDownIcon className="w-5 h-5 text-yellow-400 flex-shrink-0" />
                        )}
                      </button>
                      {isOpen && (
                        <div className="px-6 pb-4">
                          <p className="text-gray-300 leading-relaxed">{item.answer}</p>
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </div>
          ))}
        </div>

        {/* No Results */}
        {searchTerm && filteredFAQs.length === 0 && (
          <div className="text-center py-12">
            <p className="text-gray-400 text-lg mb-4">No FAQ items found for "{searchTerm}"</p>
            <button
              onClick={() => setSearchTerm('')}
              className="text-yellow-400 hover:text-yellow-300 transition-colors duration-200"
            >
              Clear search
            </button>
          </div>
        )}

        {/* Contact Support */}
        <div className="mt-12 text-center">
          <h3 className="text-xl font-semibold text-white mb-4">Didn't find what you were looking for?</h3>
          <p className="text-gray-300 mb-6">
            Our support team is here to help with any questions not covered in our FAQ.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/contact" className="btn-secondary">
              Contact Support
            </Link>
            <Link href="/help/chat" className="btn-primary">
              Start Live Chat
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
