# Stripe Webhook Setup Guide

This guide will help you set up Stripe webhooks for your Forex Bot Zone application to handle payment events automatically.

## Prerequisites

- Stripe account with API keys configured
- Application deployed and accessible via HTTPS (for production)
- For local development: ngrok or similar tunneling service

## Step 1: Get Your Webhook Endpoint URL

### For Production
Your webhook endpoint will be:
```
https://yourdomain.com/api/webhooks/stripe
```

### For Local Development
1. Install ngrok: `npm install -g ngrok`
2. Start your Next.js app: `npm run dev`
3. In another terminal, expose your local server: `ngrok http 3000`
4. Copy the HTTPS URL (e.g., `https://abc123.ngrok.io`)
5. Your webhook endpoint will be: `https://abc123.ngrok.io/api/webhooks/stripe`

## Step 2: Create Webhook in Stripe Dashboard

1. **Login to Stripe Dashboard**
   - Go to [https://dashboard.stripe.com](https://dashboard.stripe.com)
   - Make sure you're in the correct mode (Test/Live)

2. **Navigate to Webhooks**
   - In the left sidebar, click on "Developers"
   - Click on "Webhooks"

3. **Add Endpoint**
   - Click "Add endpoint" button
   - Enter your webhook URL: `https://yourdomain.com/api/webhooks/stripe`
   - Click "Select events"

4. **Select Events to Listen For**
   Select these events that your application handles:
   ```
   ✅ payment_intent.succeeded
   ✅ payment_intent.payment_failed
   ✅ invoice.payment_succeeded
   ✅ customer.subscription.created
   ✅ customer.subscription.updated
   ✅ customer.subscription.deleted
   ```

5. **Add Endpoint**
   - Click "Add endpoint" to create the webhook

## Step 3: Get Webhook Secret

1. **Find Your Webhook**
   - In the Webhooks list, click on the webhook you just created

2. **Reveal Webhook Secret**
   - In the webhook details page, find the "Signing secret" section
   - Click "Reveal" to show the secret
   - Copy the secret (it starts with `whsec_`)

## Step 4: Configure Environment Variables

Add the webhook secret to your `.env` file:

```env
# Add this line to your .env file
STRIPE_WEBHOOK_SECRET="whsec_your_actual_webhook_secret_here"
```

**Important:** 
- Replace `whsec_your_actual_webhook_secret_here` with your actual webhook secret
- Never commit this secret to version control
- Use different secrets for test and live modes

## Step 5: Test Your Webhook

### Using Stripe CLI (Recommended for Development)

1. **Install Stripe CLI**
   ```bash
   # macOS
   brew install stripe/stripe-cli/stripe
   
   # Windows
   scoop bucket add stripe https://github.com/stripe/scoop-stripe-cli.git
   scoop install stripe
   
   # Or download from: https://github.com/stripe/stripe-cli/releases
   ```

2. **Login to Stripe CLI**
   ```bash
   stripe login
   ```

3. **Forward Events to Local Server**
   ```bash
   stripe listen --forward-to localhost:3000/api/webhooks/stripe
   ```

4. **Trigger Test Events**
   ```bash
   # Test successful payment
   stripe trigger payment_intent.succeeded
   
   # Test failed payment
   stripe trigger payment_intent.payment_failed
   ```

### Using Stripe Dashboard

1. Go to your webhook in the Stripe Dashboard
2. Click on the "Send test webhook" button
3. Select an event type and click "Send test webhook"
4. Check your application logs to see if the webhook was received

## Step 6: Verify Webhook is Working

### Check Application Logs
Your application should log webhook events:
```
Webhook received: payment_intent.succeeded
Order ORD-1234567890-ABC123 completed successfully
```

### Check Database
- Orders should be updated to "COMPLETED" status
- Download records should be created for purchased products
- User's cart should be cleared

### Check Email
- Order confirmation emails should be sent (once implemented)

## Webhook Events Handled

| Event | Description | Action Taken |
|-------|-------------|--------------|
| `payment_intent.succeeded` | Payment completed successfully | Update order status, create downloads, clear cart, send confirmation email |
| `payment_intent.payment_failed` | Payment failed | Update order status to failed, log error |
| `invoice.payment_succeeded` | Subscription payment succeeded | Update membership status |
| `customer.subscription.created` | New subscription created | Create membership record |
| `customer.subscription.updated` | Subscription modified | Update membership details |
| `customer.subscription.deleted` | Subscription cancelled | Cancel membership |

## Troubleshooting

### Common Issues

1. **Webhook Secret Mismatch**
   - Error: "Invalid signature"
   - Solution: Verify `STRIPE_WEBHOOK_SECRET` matches the secret in Stripe Dashboard

2. **Endpoint Not Reachable**
   - Error: "Webhook endpoint returned non-2xx response"
   - Solution: Ensure your server is running and accessible via HTTPS

3. **Missing Events**
   - Issue: Webhooks not triggering expected actions
   - Solution: Check that you've selected the correct events in Stripe Dashboard

4. **Local Development Issues**
   - Issue: Webhooks not reaching local server
   - Solution: Use ngrok or Stripe CLI forwarding

### Debug Mode

Enable debug logging by adding to your webhook handler:
```javascript
console.log('Webhook received:', event.type, event.data.object)
```

### Webhook Logs

Check webhook delivery attempts in Stripe Dashboard:
1. Go to Developers > Webhooks
2. Click on your webhook
3. View the "Recent deliveries" section

## Security Best Practices

1. **Always Verify Signatures**
   - The webhook handler already includes signature verification
   - Never process webhooks without verifying the signature

2. **Use HTTPS**
   - Webhooks must be delivered over HTTPS in production
   - Stripe will not send webhooks to HTTP endpoints

3. **Handle Idempotency**
   - Stripe may send the same webhook multiple times
   - Your handler should be idempotent (safe to run multiple times)

4. **Secure Your Endpoint**
   - The webhook endpoint is public but secured by signature verification
   - Don't add additional authentication that would block Stripe

## Production Deployment

When deploying to production:

1. **Update Webhook URL**
   - Change the endpoint URL in Stripe Dashboard to your production domain
   - Use your live mode Stripe keys and webhook secret

2. **Environment Variables**
   - Set `STRIPE_WEBHOOK_SECRET` with your live mode webhook secret
   - Ensure all other Stripe environment variables are set correctly

3. **Test in Production**
   - Make a small test purchase to verify the entire flow works
   - Check that orders are completed and emails are sent

## Support

If you encounter issues:
1. Check the application logs for error messages
2. Review webhook delivery logs in Stripe Dashboard
3. Verify all environment variables are set correctly
4. Test with Stripe CLI for local development issues

For Stripe-specific issues, consult the [Stripe Webhooks Documentation](https://stripe.com/docs/webhooks).
