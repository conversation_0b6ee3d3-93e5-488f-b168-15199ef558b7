import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'
import { z } from 'zod'
import { ProductStatus } from '@prisma/client'

const updateProductSchema = z.object({
  name: z.string().min(1).optional(),
  slug: z.string().min(1).optional(),
  description: z.string().optional(),
  shortDescription: z.string().optional(),
  price: z.number().min(0).optional(),
  originalPrice: z.number().optional(),
  isOnSale: z.boolean().optional(),
  salePrice: z.number().optional(),
  categoryId: z.string().optional(),
  status: z.nativeEnum(ProductStatus).optional(),
  featured: z.boolean().optional(),
  downloadLimit: z.number().optional(),
  downloadExpiry: z.number().optional(),
  tags: z.array(z.string()).optional(),
  metaTitle: z.string().optional(),
  metaDescription: z.string().optional(),
  images: z.array(z.string()).optional(),
  fileKey: z.string().optional(),
  fileName: z.string().optional(),
  fileSize: z.number().optional(),
})

// GET /api/products/[id] - Get single product
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        category: true,
        downloadFiles: true,
        reviews: {
          include: {
            user: {
              select: {
                id: true,
                firstName: true,
                lastName: true,
                avatar: true
              }
            }
          },
          orderBy: { createdAt: 'desc' }
        },
        _count: {
          select: {
            reviews: true,
            downloads: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json({
        success: false,
        message: 'Product not found'
      }, { status: 404 })
    }

    // Calculate average rating
    const avgRating = await prisma.review.aggregate({
      where: { productId: product.id },
      _avg: { rating: true }
    })

    // Convert BigInt to string for JSON serialization
    const serializedProduct = {
      ...product,
      fileSize: product.fileSize ? product.fileSize.toString() : null,
      downloadFiles: product.downloadFiles.map(file => ({
        ...file,
        fileSize: file.fileSize.toString()
      })),
      averageRating: avgRating._avg.rating || 0
    }

    return NextResponse.json({
      success: true,
      data: serializedProduct
    })

  } catch (error: any) {
    console.error('Error fetching product:', error)
    return NextResponse.json({
      success: false,
      message: 'Failed to fetch product'
    }, { status: 500 })
  }
}

// PUT /api/products/[id] - Update product (Admin only)
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const validatedData = updateProductSchema.parse(body)

    // Check if product exists
    const existingProduct = await prisma.product.findUnique({
      where: { id: params.id }
    })

    if (!existingProduct) {
      return NextResponse.json({
        success: false,
        message: 'Product not found'
      }, { status: 404 })
    }

    // Check if slug is unique (if being updated)
    if (validatedData.slug && validatedData.slug !== existingProduct.slug) {
      const slugExists = await prisma.product.findUnique({
        where: { slug: validatedData.slug }
      })

      if (slugExists) {
        return NextResponse.json({
          success: false,
          message: 'Product slug already exists'
        }, { status: 400 })
      }
    }

    // Verify category exists (if being updated)
    if (validatedData.categoryId) {
      const category = await prisma.category.findUnique({
        where: { id: validatedData.categoryId }
      })

      if (!category) {
        return NextResponse.json({
          success: false,
          message: 'Category not found'
        }, { status: 400 })
      }
    }

    // Prepare data for database (convert fileSize to BigInt if present)
    const updateData = {
      ...validatedData,
      fileSize: validatedData.fileSize ? BigInt(validatedData.fileSize) : undefined
    }

    const updatedProduct = await prisma.product.update({
      where: { id: params.id },
      data: updateData,
      include: {
        category: true,
        downloadFiles: true
      }
    })

    // Convert BigInt to string for JSON serialization
    const serializedProduct = {
      ...updatedProduct,
      fileSize: updatedProduct.fileSize ? updatedProduct.fileSize.toString() : null,
      downloadFiles: updatedProduct.downloadFiles.map(file => ({
        ...file,
        fileSize: file.fileSize.toString()
      }))
    }

    return NextResponse.json({
      success: true,
      message: 'Product updated successfully',
      data: serializedProduct
    })

  } catch (error: any) {
    console.error('Error updating product:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to update product'
    }, { status: 500 })
  }
}

// DELETE /api/products/[id] - Delete product (Admin only)
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    await requireAdmin()

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: params.id },
      include: {
        downloadFiles: true
      }
    })

    if (!product) {
      return NextResponse.json({
        success: false,
        message: 'Product not found'
      }, { status: 404 })
    }

    // TODO: Delete associated files from R2
    // for (const file of product.downloadFiles) {
    //   await deleteFile(file.fileUrl)
    // }

    // Delete product (cascade will handle related records)
    await prisma.product.delete({
      where: { id: params.id }
    })

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully'
    })

  } catch (error: any) {
    console.error('Error deleting product:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to delete product'
    }, { status: 500 })
  }
}
