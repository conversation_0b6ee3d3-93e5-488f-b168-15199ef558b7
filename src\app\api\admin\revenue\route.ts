import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { requireAdmin } from '@/lib/auth-utils'

export async function GET(request: NextRequest) {
  try {
    await requireAdmin()

    const { searchParams } = new URL(request.url)
    const range = searchParams.get('range') || '30d'

    // Calculate date range
    const now = new Date()
    let startDate = new Date()
    
    switch (range) {
      case '7d':
        startDate.setDate(now.getDate() - 7)
        break
      case '30d':
        startDate.setDate(now.getDate() - 30)
        break
      case '90d':
        startDate.setDate(now.getDate() - 90)
        break
      case '1y':
        startDate.setFullYear(now.getFullYear() - 1)
        break
      default:
        startDate.setDate(now.getDate() - 30)
    }

    // Get revenue data
    const [
      totalRevenue,
      monthlyRevenue,
      dailyRevenue,
      recentTransactions
    ] = await Promise.all([
      prisma.order.aggregate({
        _sum: {
          totalAmount: true
        }
      }),

      prisma.order.aggregate({
        where: {
          createdAt: {
            gte: startDate
          }
        },
        _sum: {
          totalAmount: true
        }
      }),

      prisma.order.aggregate({
        where: {
          createdAt: {
            gte: new Date(now.getTime() - 24 * 60 * 60 * 1000) // Last 24 hours
          }
        },
        _sum: {
          totalAmount: true
        }
      }),

      prisma.order.findMany({
        take: 5,
        orderBy: {
          createdAt: 'desc'
        },
        select: {
          id: true,
          totalAmount: true,
          createdAt: true,
          status: true
        }
      })
    ])

    // Mock payment methods data - in real app, you'd track this
    const paymentMethods = [
      { method: 'PayPal', amount: Number(totalRevenue._sum.totalAmount || 0) * 0.623, percentage: 62.3 },
      { method: 'Stripe', amount: Number(totalRevenue._sum.totalAmount || 0) * 0.334, percentage: 33.4 },
      { method: 'Crypto', amount: Number(totalRevenue._sum.totalAmount || 0) * 0.043, percentage: 4.3 },
    ]

    // Calculate growth rate (mock)
    const revenueGrowth = 23.5

    const revenue = {
      totalRevenue: Number(totalRevenue._sum.totalAmount || 0),
      monthlyRevenue: Number(monthlyRevenue._sum.totalAmount || 0),
      dailyRevenue: Number(dailyRevenue._sum.totalAmount || 0),
      revenueGrowth,
      paymentMethods,
      recentTransactions: recentTransactions.map(transaction => ({
        id: transaction.id.slice(-8),
        amount: Number(transaction.totalAmount),
        method: 'PayPal', // Mock - in real app, you'd store this
        date: transaction.createdAt.toISOString().split('T')[0],
        status: transaction.status.toLowerCase()
      }))
    }

    return NextResponse.json({
      success: true,
      data: revenue
    })

  } catch (error: any) {
    console.error('Error fetching revenue:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch revenue'
    }, { status: 500 })
  }
}
