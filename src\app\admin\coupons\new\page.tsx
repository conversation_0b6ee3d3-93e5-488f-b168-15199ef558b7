'use client'

import { useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import Link from 'next/link'
import {
  ArrowLeftIcon,
  TagIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  UsersIcon
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

export default function NewCouponPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    code: '',
    name: '',
    description: '',
    type: 'PERCENTAGE' as 'FIXED_AMOUNT' | 'PERCENTAGE' | 'FREE_SHIPPING',
    value: '',
    minimumAmount: '',
    maximumDiscount: '',
    usageLimit: '',
    userUsageLimit: '',
    isActive: true,
    startsAt: '',
    expiresAt: ''
  })

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    try {
      // Prepare data for submission
      const submitData = {
        code: formData.code.toUpperCase(),
        name: formData.name,
        description: formData.description || undefined,
        type: formData.type,
        value: parseFloat(formData.value),
        minimumAmount: formData.minimumAmount ? parseFloat(formData.minimumAmount) : undefined,
        maximumDiscount: formData.maximumDiscount ? parseFloat(formData.maximumDiscount) : undefined,
        usageLimit: formData.usageLimit ? parseInt(formData.usageLimit) : undefined,
        userUsageLimit: formData.userUsageLimit ? parseInt(formData.userUsageLimit) : undefined,
        isActive: formData.isActive,
        startsAt: formData.startsAt || undefined,
        expiresAt: formData.expiresAt || undefined
      }

      const response = await fetch('/api/admin/coupons', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(submitData)
      })

      const data = await response.json()

      if (data.success) {
        toast.success('Coupon created successfully!')
        router.push('/admin/coupons')
      } else {
        toast.error(data.message || 'Failed to create coupon')
      }
    } catch (error) {
      console.error('Error creating coupon:', error)
      toast.error('Failed to create coupon')
    } finally {
      setIsSubmitting(false)
    }
  }

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  if (status === 'unauthenticated' || session?.user?.role !== 'ADMIN') {
    router.push('/admin/login')
    return null
  }

  return (
    <div className="min-h-screen py-8">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            href="/admin/coupons" 
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors duration-200 mb-4"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Coupons
          </Link>
          <h1 className="text-3xl font-bold text-white">Create New Coupon</h1>
          <p className="text-gray-400 mt-2">Create a new discount coupon for your customers</p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
            <div className="flex items-center space-x-2 mb-4">
              <TagIcon className="w-5 h-5 text-yellow-400" />
              <h2 className="text-xl font-semibold text-white">Basic Information</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Coupon Code *
                </label>
                <input
                  type="text"
                  name="code"
                  value={formData.code}
                  onChange={handleInputChange}
                  placeholder="e.g., SAVE20"
                  required
                  className="trading-input uppercase"
                  style={{ textTransform: 'uppercase' }}
                />
                <p className="text-xs text-gray-400 mt-1">
                  Will be automatically converted to uppercase
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Coupon Name *
                </label>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="e.g., 20% Off Sale"
                  required
                  className="trading-input"
                />
              </div>
            </div>

            <div className="mt-6">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                Description
              </label>
              <textarea
                name="description"
                value={formData.description}
                onChange={handleInputChange}
                placeholder="Optional description for internal use"
                rows={3}
                className="trading-input"
              />
            </div>
          </div>

          {/* Discount Settings */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
            <div className="flex items-center space-x-2 mb-4">
              <CurrencyDollarIcon className="w-5 h-5 text-yellow-400" />
              <h2 className="text-xl font-semibold text-white">Discount Settings</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Discount Type *
                </label>
                <select
                  name="type"
                  value={formData.type}
                  onChange={handleInputChange}
                  required
                  className="trading-input"
                >
                  <option value="PERCENTAGE">Percentage Off</option>
                  <option value="FIXED_AMOUNT">Fixed Amount Off</option>
                  <option value="FREE_SHIPPING">Free Shipping</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Discount Value *
                </label>
                <div className="relative">
                  {formData.type === 'PERCENTAGE' && (
                    <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400">%</span>
                  )}
                  {formData.type === 'FIXED_AMOUNT' && (
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">$</span>
                  )}
                  <input
                    type="number"
                    name="value"
                    value={formData.value}
                    onChange={handleInputChange}
                    placeholder={formData.type === 'PERCENTAGE' ? '20' : '10.00'}
                    min="0"
                    max={formData.type === 'PERCENTAGE' ? '100' : undefined}
                    step={formData.type === 'PERCENTAGE' ? '1' : '0.01'}
                    required={formData.type !== 'FREE_SHIPPING'}
                    disabled={formData.type === 'FREE_SHIPPING'}
                    className={`trading-input ${formData.type === 'FIXED_AMOUNT' ? 'pl-8' : ''} ${formData.type === 'PERCENTAGE' ? 'pr-8' : ''}`}
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Minimum Order Amount
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">$</span>
                  <input
                    type="number"
                    name="minimumAmount"
                    value={formData.minimumAmount}
                    onChange={handleInputChange}
                    placeholder="0.00"
                    min="0"
                    step="0.01"
                    className="trading-input pl-8"
                  />
                </div>
                <p className="text-xs text-gray-400 mt-1">
                  Minimum order amount required to use this coupon
                </p>
              </div>
              
              {formData.type === 'PERCENTAGE' && (
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-2">
                    Maximum Discount Amount
                  </label>
                  <div className="relative">
                    <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">$</span>
                    <input
                      type="number"
                      name="maximumDiscount"
                      value={formData.maximumDiscount}
                      onChange={handleInputChange}
                      placeholder="100.00"
                      min="0"
                      step="0.01"
                      className="trading-input pl-8"
                    />
                  </div>
                  <p className="text-xs text-gray-400 mt-1">
                    Maximum discount amount for percentage coupons
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Usage Limits */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
            <div className="flex items-center space-x-2 mb-4">
              <UsersIcon className="w-5 h-5 text-yellow-400" />
              <h2 className="text-xl font-semibold text-white">Usage Limits</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Total Usage Limit
                </label>
                <input
                  type="number"
                  name="usageLimit"
                  value={formData.usageLimit}
                  onChange={handleInputChange}
                  placeholder="Unlimited"
                  min="1"
                  className="trading-input"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Maximum number of times this coupon can be used
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Per User Usage Limit
                </label>
                <input
                  type="number"
                  name="userUsageLimit"
                  value={formData.userUsageLimit}
                  onChange={handleInputChange}
                  placeholder="Unlimited"
                  min="1"
                  className="trading-input"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Maximum times each user can use this coupon
                </p>
              </div>
            </div>
          </div>

          {/* Schedule */}
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
            <div className="flex items-center space-x-2 mb-4">
              <CalendarIcon className="w-5 h-5 text-yellow-400" />
              <h2 className="text-xl font-semibold text-white">Schedule</h2>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  Start Date & Time
                </label>
                <input
                  type="datetime-local"
                  name="startsAt"
                  value={formData.startsAt}
                  onChange={handleInputChange}
                  className="trading-input"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Leave empty to start immediately
                </p>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-2">
                  End Date & Time
                </label>
                <input
                  type="datetime-local"
                  name="expiresAt"
                  value={formData.expiresAt}
                  onChange={handleInputChange}
                  className="trading-input"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Leave empty for no expiration
                </p>
              </div>
            </div>

            <div className="mt-6">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  name="isActive"
                  checked={formData.isActive}
                  onChange={handleInputChange}
                  className="rounded border-gray-600 text-yellow-400 focus:ring-yellow-400"
                />
                <span className="text-gray-300">Activate coupon immediately</span>
              </label>
            </div>
          </div>

          {/* Actions */}
          <div className="flex items-center justify-end space-x-4">
            <Link href="/admin/coupons">
              <Button variant="outline" className="border-gray-600 text-gray-300 hover:bg-gray-700">
                Cancel
              </Button>
            </Link>
            <Button
              type="submit"
              disabled={isSubmitting}
              variant="premium"
            >
              {isSubmitting ? (
                <>
                  <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin mr-2" />
                  Creating...
                </>
              ) : (
                'Create Coupon'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  )
}
