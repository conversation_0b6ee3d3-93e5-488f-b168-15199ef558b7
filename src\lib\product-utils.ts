import { prisma } from '@/lib/prisma'
import { ProductWithRelations, ProductFilters } from '@/types'
import { ProductStatus } from '@prisma/client'

export async function getProducts(filters: ProductFilters = {}) {
  const {
    category,
    minPrice,
    maxPrice,
    featured,
    onSale,
    tags,
    search,
    sortBy = 'createdAt',
    sortOrder = 'desc'
  } = filters

  // Build where clause
  const where: any = {
    status: ProductStatus.PUBLISHED
  }

  if (category) {
    where.category = {
      slug: category
    }
  }

  if (search) {
    where.OR = [
      { name: { contains: search, mode: 'insensitive' } },
      { description: { contains: search, mode: 'insensitive' } },
      { shortDescription: { contains: search, mode: 'insensitive' } },
      { tags: { hasSome: [search] } }
    ]
  }

  if (featured !== undefined) {
    where.featured = featured
  }

  if (onSale !== undefined) {
    where.isOnSale = onSale
  }

  if (minPrice !== undefined || maxPrice !== undefined) {
    where.price = {}
    if (minPrice !== undefined) where.price.gte = minPrice
    if (maxPrice !== undefined) where.price.lte = maxPrice
  }

  if (tags && tags.length > 0) {
    where.tags = {
      hasSome: tags
    }
  }

  // Build orderBy clause
  const orderBy: any = {}
  if (sortBy === 'price') {
    orderBy.price = sortOrder
  } else if (sortBy === 'name') {
    orderBy.name = sortOrder
  } else if (sortBy === 'rating') {
    // This would require a more complex query with aggregation
    orderBy.createdAt = sortOrder
  } else {
    orderBy.createdAt = sortOrder
  }

  const products = await prisma.product.findMany({
    where,
    include: {
      category: true,
      _count: {
        select: {
          reviews: true,
          downloads: true
        }
      }
    },
    orderBy
  })

  // Calculate average ratings and serialize Decimal fields
  const productsWithRatings = await Promise.all(
    products.map(async (product) => {
      const avgRating = await prisma.review.aggregate({
        where: { productId: product.id },
        _avg: { rating: true }
      })

      return {
        ...product,
        price: Number(product.price),
        originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
        salePrice: product.salePrice ? Number(product.salePrice) : null,
        averageRating: avgRating._avg.rating || 0
      }
    })
  )

  return productsWithRatings
}

export async function getProductBySlug(slug: string): Promise<ProductWithRelations | null> {
  const product = await prisma.product.findUnique({
    where: { 
      slug,
      status: ProductStatus.PUBLISHED
    },
    include: {
      category: true,
      reviews: {
        include: {
          user: {
            select: {
              id: true,
              firstName: true,
              lastName: true,
              avatar: true
            }
          }
        },
        orderBy: { createdAt: 'desc' }
      },
      _count: {
        select: {
          reviews: true,
          downloads: true
        }
      }
    }
  })

  if (!product) return null

  // Calculate average rating
  const avgRating = await prisma.review.aggregate({
    where: { productId: product.id },
    _avg: { rating: true }
  })

  return {
    ...product,
    price: Number(product.price),
    originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
    salePrice: product.salePrice ? Number(product.salePrice) : null,
    averageRating: avgRating._avg.rating || 0
  }
}

export async function getFeaturedProducts(limit: number = 8): Promise<ProductWithRelations[]> {
  const products = await prisma.product.findMany({
    where: {
      status: ProductStatus.PUBLISHED,
      featured: true
    },
    include: {
      category: true,
      _count: {
        select: {
          reviews: true,
          downloads: true
        }
      }
    },
    orderBy: { createdAt: 'desc' },
    take: limit
  })

  // Calculate average ratings and serialize Decimal fields
  const productsWithRatings = await Promise.all(
    products.map(async (product) => {
      const avgRating = await prisma.review.aggregate({
        where: { productId: product.id },
        _avg: { rating: true }
      })

      return {
        ...product,
        price: Number(product.price),
        originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
        salePrice: product.salePrice ? Number(product.salePrice) : null,
        averageRating: avgRating._avg.rating || 0
      }
    })
  )

  return productsWithRatings
}

export async function getRelatedProducts(productId: string, categoryId: string, limit: number = 4): Promise<ProductWithRelations[]> {
  const products = await prisma.product.findMany({
    where: {
      status: ProductStatus.PUBLISHED,
      categoryId,
      id: { not: productId }
    },
    include: {
      category: true,
      _count: {
        select: {
          reviews: true,
          downloads: true
        }
      }
    },
    orderBy: { createdAt: 'desc' },
    take: limit
  })

  // Calculate average ratings and serialize Decimal fields
  const productsWithRatings = await Promise.all(
    products.map(async (product) => {
      const avgRating = await prisma.review.aggregate({
        where: { productId: product.id },
        _avg: { rating: true }
      })

      return {
        ...product,
        price: Number(product.price),
        originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
        salePrice: product.salePrice ? Number(product.salePrice) : null,
        averageRating: avgRating._avg.rating || 0
      }
    })
  )

  return productsWithRatings
}

export async function getProductsByCategory(categorySlug: string, limit?: number): Promise<ProductWithRelations[]> {
  const products = await prisma.product.findMany({
    where: {
      status: ProductStatus.PUBLISHED,
      category: {
        slug: categorySlug
      }
    },
    include: {
      category: true,
      _count: {
        select: {
          reviews: true,
          downloads: true
        }
      }
    },
    orderBy: { createdAt: 'desc' },
    take: limit
  })

  // Calculate average ratings and serialize Decimal fields
  const productsWithRatings = await Promise.all(
    products.map(async (product) => {
      const avgRating = await prisma.review.aggregate({
        where: { productId: product.id },
        _avg: { rating: true }
      })

      return {
        ...product,
        price: Number(product.price),
        originalPrice: product.originalPrice ? Number(product.originalPrice) : null,
        salePrice: product.salePrice ? Number(product.salePrice) : null,
        averageRating: avgRating._avg.rating || 0
      }
    })
  )

  return productsWithRatings
}

export function generateProductSlug(name: string): string {
  return name
    .toLowerCase()
    .replace(/[^a-z0-9\s-]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single
    .trim()
}

export function calculateDiscountPercentage(originalPrice: number, salePrice: number): number {
  if (originalPrice <= 0 || salePrice <= 0 || salePrice >= originalPrice) {
    return 0
  }
  return Math.round(((originalPrice - salePrice) / originalPrice) * 100)
}

export function formatPrice(price: number, currency: string = 'USD'): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(price)
}

export function isProductOnSale(product: any): boolean {
  return product.isOnSale && product.salePrice && product.salePrice < product.price
}

export function getEffectivePrice(product: any): number {
  return isProductOnSale(product) ? product.salePrice : product.price
}
