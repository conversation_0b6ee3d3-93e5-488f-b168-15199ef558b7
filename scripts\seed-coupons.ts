import { PrismaClient, CouponType } from '@prisma/client'

const prisma = new PrismaClient()

async function seedCoupons() {
  console.log('🎫 Starting coupon seed...')

  const coupons = [
    {
      code: 'WELCOME10',
      name: 'Welcome 10% Off',
      description: 'Welcome discount for new customers',
      type: CouponType.PERCENTAGE,
      value: 10,
      minimumAmount: 50,
      usageLimit: 100,
      userUsageLimit: 1,
      isActive: true,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days from now
    },
    {
      code: 'SAVE25',
      name: '$25 Off Orders Over $100',
      description: 'Fixed amount discount for larger orders',
      type: CouponType.FIXED_AMOUNT,
      value: 25,
      minimumAmount: 100,
      usageLimit: 50,
      userUsageLimit: 2,
      isActive: true,
      expiresAt: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000) // 60 days from now
    },
    {
      code: 'BLACKFRIDAY',
      name: 'Black Friday 30% Off',
      description: 'Black Friday special discount',
      type: CouponType.PERCENTAGE,
      value: 30,
      maximumDiscount: 100,
      usageLimit: 200,
      userUsageLimit: 1,
      isActive: true,
      startsAt: new Date('2024-11-29'),
      expiresAt: new Date('2024-12-02')
    },
    {
      code: 'UNLIMITED20',
      name: '20% Off - No Limits',
      description: 'Unlimited usage percentage discount',
      type: CouponType.PERCENTAGE,
      value: 20,
      isActive: true
      // No usage limits or expiry
    },
    {
      code: 'EXPIRED',
      name: 'Expired Test Coupon',
      description: 'Test coupon that has expired',
      type: CouponType.PERCENTAGE,
      value: 50,
      isActive: true,
      expiresAt: new Date(Date.now() - 24 * 60 * 60 * 1000) // Yesterday
    }
  ]

  for (const couponData of coupons) {
    try {
      const coupon = await prisma.coupon.upsert({
        where: { code: couponData.code },
        update: couponData,
        create: couponData
      })
      console.log(`✅ Coupon created/updated: ${coupon.code}`)
    } catch (error) {
      console.error(`❌ Failed to create coupon ${couponData.code}:`, error)
    }
  }

  console.log('🎉 Coupon seed completed!')
}

seedCoupons()
  .catch((e) => {
    console.error('Seed failed:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
