import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const createCouponSchema = z.object({
  code: z.string().min(1, 'Coupon code is required').max(50),
  name: z.string().min(1, 'Coupon name is required').max(100),
  description: z.string().optional(),
  type: z.enum(['FIXED_AMOUNT', 'PERCENTAGE', 'FREE_SHIPPING']),
  value: z.number().min(0, 'Value must be positive'),
  minimumAmount: z.number().min(0).optional(),
  maximumDiscount: z.number().min(0).optional(),
  usageLimit: z.number().min(1).optional(),
  userUsageLimit: z.number().min(1).optional(),
  isActive: z.boolean().default(true),
  startsAt: z.string().datetime().optional(),
  expiresAt: z.string().datetime().optional()
})

// GET - List all coupons
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const search = searchParams.get('search') || ''
    const status = searchParams.get('status') // 'active', 'inactive', 'expired'

    const skip = (page - 1) * limit

    const where: any = {}
    
    if (search) {
      where.OR = [
        { code: { contains: search, mode: 'insensitive' } },
        { name: { contains: search, mode: 'insensitive' } }
      ]
    }

    if (status === 'active') {
      where.isActive = true
      where.OR = [
        { expiresAt: null },
        { expiresAt: { gt: new Date() } }
      ]
    } else if (status === 'inactive') {
      where.isActive = false
    } else if (status === 'expired') {
      where.expiresAt = { lt: new Date() }
    }

    const [coupons, total] = await Promise.all([
      prisma.coupon.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createdAt: 'desc' },
        include: {
          _count: {
            select: { couponUsages: true }
          }
        }
      }),
      prisma.coupon.count({ where })
    ])

    return NextResponse.json({
      success: true,
      data: {
        coupons,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit)
        }
      }
    })

  } catch (error: any) {
    console.error('Error fetching coupons:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to fetch coupons'
    }, { status: 500 })
  }
}

// POST - Create new coupon
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const body = await request.json()
    const data = createCouponSchema.parse(body)

    // Convert code to uppercase
    const couponData = {
      ...data,
      code: data.code.toUpperCase(),
      startsAt: data.startsAt ? new Date(data.startsAt) : null,
      expiresAt: data.expiresAt ? new Date(data.expiresAt) : null
    }

    // Validate percentage coupons
    if (data.type === 'PERCENTAGE' && data.value > 100) {
      return NextResponse.json({
        success: false,
        message: 'Percentage value cannot exceed 100%'
      }, { status: 400 })
    }

    // Check if coupon code already exists
    const existingCoupon = await prisma.coupon.findUnique({
      where: { code: couponData.code }
    })

    if (existingCoupon) {
      return NextResponse.json({
        success: false,
        message: 'Coupon code already exists'
      }, { status: 400 })
    }

    const coupon = await prisma.coupon.create({
      data: couponData
    })

    return NextResponse.json({
      success: true,
      data: coupon
    })

  } catch (error: any) {
    console.error('Error creating coupon:', error)
    
    if (error.name === 'ZodError') {
      return NextResponse.json({
        success: false,
        message: 'Validation error',
        errors: error.errors
      }, { status: 400 })
    }

    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to create coupon'
    }, { status: 500 })
  }
}
