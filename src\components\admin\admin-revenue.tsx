'use client'

import { useEffect, useState } from 'react'
import {
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
  ArrowTrendingDownIcon,
  CalendarIcon,
  CreditCardIcon,
  BanknotesIcon
} from '@heroicons/react/24/outline'

interface RevenueData {
  totalRevenue: number
  monthlyRevenue: number
  dailyRevenue: number
  revenueGrowth: number
  paymentMethods: Array<{
    method: string
    amount: number
    percentage: number
  }>
  recentTransactions: Array<{
    id: string
    amount: number
    method: string
    date: string
    status: string
  }>
}

export function AdminRevenue() {
  const [revenue, setRevenue] = useState<RevenueData | null>(null)
  const [loading, setLoading] = useState(true)
  const [timeRange, setTimeRange] = useState('30d')

  useEffect(() => {
    const fetchRevenue = async () => {
      try {
        const response = await fetch(`/api/admin/revenue?range=${timeRange}`)
        if (response.ok) {
          const data = await response.json()
          setRevenue(data.data)
        }
      } catch (error) {
        console.error('Error fetching revenue:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchRevenue()
  }, [timeRange])

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400"></div>
      </div>
    )
  }

  // Mock data for demonstration
  const mockRevenue: RevenueData = {
    totalRevenue: 45678.90,
    monthlyRevenue: 12345.67,
    dailyRevenue: 456.78,
    revenueGrowth: 23.5,
    paymentMethods: [
      { method: 'PayPal', amount: 28456.78, percentage: 62.3 },
      { method: 'Stripe', amount: 15234.56, percentage: 33.4 },
      { method: 'Crypto', amount: 1987.56, percentage: 4.3 },
    ],
    recentTransactions: [
      { id: 'TXN001', amount: 59.99, method: 'PayPal', date: '2024-01-15', status: 'completed' },
      { id: 'TXN002', amount: 49.99, method: 'Stripe', date: '2024-01-15', status: 'completed' },
      { id: 'TXN003', amount: 79.99, method: 'PayPal', date: '2024-01-14', status: 'completed' },
      { id: 'TXN004', amount: 39.99, method: 'Crypto', date: '2024-01-14', status: 'pending' },
      { id: 'TXN005', amount: 69.99, method: 'Stripe', date: '2024-01-13', status: 'completed' },
    ]
  }

  const data = revenue || mockRevenue

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex items-center justify-between">
        <div className="flex space-x-2">
          {[
            { value: '7d', label: '7 Days' },
            { value: '30d', label: '30 Days' },
            { value: '90d', label: '90 Days' },
            { value: '1y', label: '1 Year' },
          ].map((range) => (
            <button
              key={range.value}
              onClick={() => setTimeRange(range.value)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                timeRange === range.value
                  ? 'bg-yellow-400 text-black'
                  : 'bg-white/10 text-gray-300 hover:bg-white/20'
              }`}
            >
              {range.label}
            </button>
          ))}
        </div>
      </div>

      {/* Revenue Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CurrencyDollarIcon className="h-8 w-8 text-green-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Total Revenue</p>
              <p className="text-2xl font-bold text-white">${data.totalRevenue.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CalendarIcon className="h-8 w-8 text-blue-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Monthly Revenue</p>
              <p className="text-2xl font-bold text-white">${data.monthlyRevenue.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <BanknotesIcon className="h-8 w-8 text-yellow-400" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Daily Revenue</p>
              <p className="text-2xl font-bold text-white">${data.dailyRevenue.toFixed(2)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              {data.revenueGrowth >= 0 ? (
                <ArrowTrendingUpIcon className="h-8 w-8 text-green-400" />
              ) : (
                <ArrowTrendingDownIcon className="h-8 w-8 text-red-400" />
              )}
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-400">Growth Rate</p>
              <p className={`text-2xl font-bold ${data.revenueGrowth >= 0 ? 'text-green-400' : 'text-red-400'}`}>
                {data.revenueGrowth >= 0 ? '+' : ''}{data.revenueGrowth}%
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Payment Methods */}
        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <h3 className="text-lg font-medium text-white mb-4">Payment Methods</h3>
          <div className="space-y-4">
            {data.paymentMethods.map((method, index) => (
              <div key={index} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <CreditCardIcon className="h-5 w-5 text-gray-400" />
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-white">{method.method}</p>
                    <p className="text-xs text-gray-400">{method.percentage}% of total</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-white">${method.amount.toLocaleString()}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Recent Transactions */}
        <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
          <h3 className="text-lg font-medium text-white mb-4">Recent Transactions</h3>
          <div className="space-y-4">
            {data.recentTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className="w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center">
                      <span className="text-xs font-bold text-black">$</span>
                    </div>
                  </div>
                  <div className="ml-3">
                    <p className="text-sm font-medium text-white">{transaction.id}</p>
                    <p className="text-xs text-gray-400">{transaction.method} • {transaction.date}</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm font-medium text-white">${transaction.amount}</p>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    transaction.status === 'completed' 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {transaction.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}
