'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import { 
  MagnifyingGlassIcon, 
  XMarkIcon,
  StarIcon,
  ArrowRightIcon
} from '@heroicons/react/24/outline'
import { Button } from '@/components/ui/button'

interface Product {
  id: string
  name: string
  slug: string
  price: number
  originalPrice?: number
  isOnSale: boolean
  salePrice?: number
  images: string[]
  shortDescription?: string
  category: {
    name: string
    slug: string
  }
  _count: {
    reviews: number
  }
  averageRating?: number
}

interface SearchModalProps {
  isOpen: boolean
  onClose: () => void
}

export function SearchModal({ isOpen, onClose }: SearchModalProps) {
  const [searchTerm, setSearchTerm] = useState('')
  const [results, setResults] = useState<Product[]>([])
  const [loading, setLoading] = useState(false)
  const [showResults, setShowResults] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const router = useRouter()

  // Focus input when modal opens
  useEffect(() => {
    if (isOpen && inputRef.current) {
      inputRef.current.focus()
    }
  }, [isOpen])

  // Search products
  useEffect(() => {
    const searchProducts = async () => {
      if (!searchTerm.trim()) {
        setResults([])
        setShowResults(false)
        return
      }

      setLoading(true)
      try {
        const response = await fetch(`/api/products?search=${encodeURIComponent(searchTerm)}&limit=6`)
        if (response.ok) {
          const data = await response.json()
          setResults(data.data || [])
          setShowResults(true)
        }
      } catch (error) {
        console.error('Search error:', error)
        setResults([])
      } finally {
        setLoading(false)
      }
    }

    const debounceTimer = setTimeout(searchProducts, 300)
    return () => clearTimeout(debounceTimer)
  }, [searchTerm])

  const handleClose = () => {
    setSearchTerm('')
    setResults([])
    setShowResults(false)
    onClose()
  }

  const handleViewAllResults = () => {
    router.push(`/products?search=${encodeURIComponent(searchTerm)}`)
    handleClose()
  }

  const handleProductClick = () => {
    handleClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      {/* Backdrop */}
      <div 
        className="fixed inset-0 bg-black/60 backdrop-blur-sm transition-opacity"
        onClick={handleClose}
      />
      
      {/* Modal */}
      <div className="relative min-h-screen flex items-start justify-center p-2 sm:p-4 pt-8 sm:pt-16">
        <div className="relative w-full max-w-2xl bg-gray-900/95 backdrop-blur-xl border border-gray-700/50 rounded-xl sm:rounded-2xl shadow-2xl">
          {/* Header */}
          <div className="flex items-center p-4 sm:p-6 border-b border-gray-700/50">
            <MagnifyingGlassIcon className="w-5 h-5 sm:w-6 sm:h-6 text-gray-400 mr-3" />
            <input
              ref={inputRef}
              type="text"
              placeholder="Search for Expert Advisors, Indicators..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="flex-1 bg-transparent text-white placeholder-gray-400 text-base sm:text-lg focus:outline-none"
            />
            <button
              onClick={handleClose}
              className="ml-3 text-gray-400 hover:text-white transition-colors p-1"
            >
              <XMarkIcon className="w-5 h-5 sm:w-6 sm:h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="max-h-96 overflow-y-auto">
            {loading && (
              <div className="flex items-center justify-center py-12">
                <div className="w-8 h-8 border-2 border-yellow-400 border-t-transparent rounded-full animate-spin" />
              </div>
            )}

            {!loading && searchTerm && !showResults && (
              <div className="text-center py-12 text-gray-400">
                Start typing to search...
              </div>
            )}

            {!loading && showResults && results.length === 0 && (
              <div className="text-center py-12">
                <p className="text-gray-400 mb-4">No products found for "{searchTerm}"</p>
                <Link href="/products" onClick={handleClose}>
                  <Button variant="outline" size="sm">
                    Browse All Products
                  </Button>
                </Link>
              </div>
            )}

            {!loading && results.length > 0 && (
              <div className="p-3 sm:p-4">
                <div className="space-y-2 sm:space-y-3">
                  {results.map((product) => (
                    <Link
                      key={product.id}
                      href={`/products/${product.slug}`}
                      onClick={handleProductClick}
                      className="flex items-center p-3 rounded-xl hover:bg-gray-800/50 transition-colors group"
                    >
                      {/* Product Image */}
                      <div className="w-12 h-12 sm:w-16 sm:h-16 bg-gray-800 rounded-lg overflow-hidden flex-shrink-0">
                        {product.images[0] ? (
                          <Image
                            src={product.images[0]}
                            alt={product.name}
                            width={64}
                            height={64}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-gray-700 to-gray-800 flex items-center justify-center">
                            <span className="text-gray-500 text-xs">No Image</span>
                          </div>
                        )}
                      </div>

                      {/* Product Info */}
                      <div className="ml-3 sm:ml-4 flex-1 min-w-0">
                        <h3 className="text-white font-medium text-sm sm:text-base truncate group-hover:text-yellow-400 transition-colors">
                          {product.name}
                        </h3>
                        <p className="text-gray-400 text-xs sm:text-sm truncate">
                          {product.category.name}
                        </p>
                        <div className="flex items-center mt-1">
                          <span className="text-yellow-400 font-bold text-sm sm:text-base">
                            ${product.isOnSale && product.salePrice ? product.salePrice : product.price}
                          </span>
                          {product.isOnSale && product.originalPrice && (
                            <span className="text-gray-500 text-xs sm:text-sm line-through ml-2">
                              ${product.originalPrice}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Arrow */}
                      <ArrowRightIcon className="w-4 h-4 sm:w-5 sm:h-5 text-gray-400 group-hover:text-yellow-400 transition-colors" />
                    </Link>
                  ))}
                </div>

                {/* View All Results */}
                <div className="mt-6 pt-4 border-t border-gray-700/50">
                  <Button
                    onClick={handleViewAllResults}
                    variant="outline"
                    className="w-full"
                  >
                    View All Results for "{searchTerm}"
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
