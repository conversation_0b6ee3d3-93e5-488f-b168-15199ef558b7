import { NextRequest, NextResponse } from 'next/server'
import { debugLogger } from '@/lib/debug'
import { asyncHandler, AuthenticationError } from '@/lib/error-handler'

export const GET = asyncHandler(async (request: NextRequest) => {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    throw new AuthenticationError('Debug logs only available in development')
  }

  const { searchParams } = new URL(request.url)
  const category = searchParams.get('category')
  const level = searchParams.get('level') as any
  const limit = parseInt(searchParams.get('limit') || '100')

  let logs = debugLogger.getLogs()

  if (category) {
    logs = debugLogger.getLogsByCategory(category)
  }

  if (level) {
    logs = debugLogger.getLogsByLevel(level)
  }

  // Limit results
  logs = logs.slice(-limit)

  return NextResponse.json({
    success: true,
    data: {
      logs,
      total: logs.length,
      categories: [...new Set(debugLogger.getLogs().map(log => log.category))],
      levels: [...new Set(debugLogger.getLogs().map(log => log.level))],
    }
  })
})

export const DELETE = asyncHandler(async (request: NextRequest) => {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    throw new AuthenticationError('Debug logs only available in development')
  }

  debugLogger.clear()

  return NextResponse.json({
    success: true,
    message: 'Debug logs cleared'
  })
})
