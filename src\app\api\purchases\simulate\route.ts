import { NextRequest, NextResponse } from 'next/server'
import { requireAuth } from '@/lib/auth-utils'
import { prisma } from '@/lib/prisma'

// POST /api/purchases/simulate - Simulate a purchase for testing (development only)
export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json({
      success: false,
      message: 'This endpoint is only available in development'
    }, { status: 403 })
  }

  try {
    const user = await requireAuth()
    const { productId } = await request.json()

    if (!productId) {
      return NextResponse.json({
        success: false,
        message: 'Product ID is required'
      }, { status: 400 })
    }

    // Check if product exists
    const product = await prisma.product.findUnique({
      where: { id: productId }
    })

    if (!product) {
      return NextResponse.json({
        success: false,
        message: 'Product not found'
      }, { status: 404 })
    }

    // Check if purchase already exists
    const existingPurchase = await prisma.purchase.findUnique({
      where: {
        userId_productId: {
          userId: user.id,
          productId: productId
        }
      }
    })

    if (existingPurchase) {
      return NextResponse.json({
        success: false,
        message: 'Product already purchased'
      }, { status: 400 })
    }

    // Create the purchase
    const purchase = await prisma.purchase.create({
      data: {
        userId: user.id,
        productId: productId,
        status: 'COMPLETED',
        price: product.price
      }
    })

    return NextResponse.json({
      success: true,
      message: 'Purchase simulated successfully',
      data: purchase
    })

  } catch (error: any) {
    console.error('Error simulating purchase:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to simulate purchase'
    }, { status: 500 })
  }
}
