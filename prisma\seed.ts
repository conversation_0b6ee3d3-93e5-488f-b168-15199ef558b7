import { PrismaClient, UserRole, ProductStatus, MembershipType } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seed...')

  // Create admin user
  const hashedPassword = await bcrypt.hash('admin123', 12)

  let adminUser
  try {
    adminUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {
        password: hashedPassword,
        role: UserRole.ADMIN,
      },
      create: {
        email: '<EMAIL>',
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        password: hashedPassword,
        role: UserRole.ADMIN,
        emailVerified: new Date(),
      },
    })
    console.log('✅ Admin user created/updated:', adminUser.email)
  } catch (error: any) {
    if (error.code === 'P2002') {
      // User already exists, try to find by email
      adminUser = await prisma.user.findUnique({
        where: { email: '<EMAIL>' }
      })
      console.log('✅ Admin user already exists:', adminUser?.email)
    } else {
      throw error
    }
  }

  // Create categories
  const categories = [
    {
      name: 'MT4 EA',
      slug: 'mt4-ea',
      description: 'Premium Expert Advisors for MetaTrader 4',
      image: '/images/categories/mt4-ea.jpg',
      sortOrder: 1,
    },
    {
      name: 'MT5 EA',
      slug: 'mt5-ea',
      description: 'Premium Expert Advisors for MetaTrader 5',
      image: '/images/categories/mt5-ea.jpg',
      sortOrder: 2,
    },
    {
      name: 'MT4 Indicators',
      slug: 'mt4-indicators',
      description: 'Professional technical indicators for MetaTrader 4',
      image: '/images/categories/mt4-indicators.jpg',
      sortOrder: 3,
    },
    {
      name: 'MT5 Indicators',
      slug: 'mt5-indicators',
      description: 'Professional technical indicators for MetaTrader 5',
      image: '/images/categories/mt5-indicators.jpg',
      sortOrder: 4,
    },
    {
      name: 'Trading Systems',
      slug: 'trading-systems',
      description: 'Complete trading systems and strategies',
      image: '/images/categories/trading-systems.jpg',
      sortOrder: 5,
    },
  ]

  const createdCategories = []
  for (const category of categories) {
    const createdCategory = await prisma.category.upsert({
      where: { slug: category.slug },
      update: {},
      create: category,
    })
    createdCategories.push(createdCategory)
    console.log('✅ Category created:', createdCategory.name)
  }

  // Create sample products - 10 for each category
  const productTemplates = {
    'MT4 EA': [
      { name: 'TrippaTrading AI EA', description: 'Advanced AI-powered Expert Advisor with machine learning capabilities and adaptive trading strategies.', price: 49.99, originalPrice: 1299.00, featured: true },
      { name: 'XG Gold Robot', description: 'Specialized gold trading EA with advanced market analysis and risk management features.', price: 59.99, originalPrice: 1099.00, featured: false },
      { name: 'Forex Scalper Pro', description: 'High-frequency scalping EA with millisecond precision and advanced order management.', price: 39.99, originalPrice: 899.00, featured: true },
      { name: 'Trend Master EA', description: 'Professional trend-following EA with dynamic lot sizing and multi-pair trading capabilities.', price: 44.99, originalPrice: 999.00, featured: false },
      { name: 'Grid Trading Bot', description: 'Advanced grid trading system with intelligent recovery mechanisms and risk control.', price: 34.99, originalPrice: 799.00, featured: false },
      { name: 'News Trader EA', description: 'High-impact news trading robot with economic calendar integration and volatility filters.', price: 54.99, originalPrice: 1199.00, featured: true },
      { name: 'Breakout Hunter', description: 'Professional breakout trading EA with pattern recognition and momentum analysis.', price: 42.99, originalPrice: 949.00, featured: false },
      { name: 'Martingale Master', description: 'Advanced martingale EA with intelligent recovery and drawdown protection systems.', price: 37.99, originalPrice: 849.00, featured: false },
      { name: 'Swing Trader Pro', description: 'Long-term swing trading EA with fundamental analysis integration and position sizing.', price: 47.99, originalPrice: 1049.00, featured: true },
      { name: 'Arbitrage EA', description: 'Multi-broker arbitrage trading system with latency optimization and execution algorithms.', price: 69.99, originalPrice: 1499.00, featured: false }
    ],
    'MT5 EA': [
      { name: 'Quantum Queen', description: 'Professional scalping EA with quantum computing algorithms and high-frequency trading capabilities.', price: 59.99, originalPrice: 1299.00, featured: true },
      { name: 'Neural Network EA', description: 'AI-powered trading robot using deep learning neural networks for market prediction.', price: 64.99, originalPrice: 1399.00, featured: true },
      { name: 'Multi-Currency EA', description: 'Advanced portfolio trading system managing multiple currency pairs simultaneously.', price: 52.99, originalPrice: 1149.00, featured: false },
      { name: 'Hedge Master Pro', description: 'Professional hedging EA with correlation analysis and risk diversification strategies.', price: 48.99, originalPrice: 1099.00, featured: false },
      { name: 'Volatility Trader', description: 'Specialized volatility trading EA with VIX integration and market sentiment analysis.', price: 41.99, originalPrice: 929.00, featured: true },
      { name: 'Copy Trading EA', description: 'Advanced signal copying system with trade filtering and money management features.', price: 36.99, originalPrice: 819.00, featured: false },
      { name: 'Fibonacci EA', description: 'Professional Fibonacci-based trading robot with retracement and extension analysis.', price: 43.99, originalPrice: 979.00, featured: false },
      { name: 'Support Resistance', description: 'Advanced S/R trading EA with dynamic level detection and breakout confirmation.', price: 45.99, originalPrice: 1019.00, featured: true },
      { name: 'Momentum Trader', description: 'High-performance momentum trading EA with acceleration indicators and trend filters.', price: 50.99, originalPrice: 1129.00, featured: false },
      { name: 'Mean Reversion EA', description: 'Statistical arbitrage EA using mean reversion strategies and pair trading techniques.', price: 55.99, originalPrice: 1249.00, featured: false }
    ]
  }

  // Add remaining product templates
  productTemplates['MT4 Indicators'] = [
    { name: 'Premium Trend Indicator', description: 'Advanced trend detection indicator with multiple timeframe analysis and signal alerts.', price: 29.99, originalPrice: 199.00, featured: true },
    { name: 'Support Resistance Pro', description: 'Professional S/R indicator with dynamic level detection and breakout signals.', price: 24.99, originalPrice: 179.00, featured: false },
    { name: 'Volume Profile Master', description: 'Advanced volume profile indicator with market structure analysis and POC detection.', price: 34.99, originalPrice: 229.00, featured: true },
    { name: 'Fibonacci Toolkit', description: 'Complete Fibonacci analysis suite with automatic retracement and extension levels.', price: 27.99, originalPrice: 189.00, featured: false },
    { name: 'Candlestick Patterns', description: 'Professional candlestick pattern recognition with reversal and continuation signals.', price: 22.99, originalPrice: 159.00, featured: false },
    { name: 'Moving Average Suite', description: 'Advanced MA indicator collection with adaptive algorithms and trend analysis.', price: 19.99, originalPrice: 139.00, featured: true },
    { name: 'Oscillator Pro', description: 'Multi-oscillator indicator with RSI, Stochastic, and MACD integration.', price: 26.99, originalPrice: 179.00, featured: false },
    { name: 'Market Structure', description: 'Professional market structure indicator with swing highs/lows and trend analysis.', price: 31.99, originalPrice: 209.00, featured: false },
    { name: 'Price Action Signals', description: 'Advanced price action indicator with pin bars, inside bars, and engulfing patterns.', price: 25.99, originalPrice: 169.00, featured: true },
    { name: 'Volatility Meter', description: 'Real-time volatility measurement tool with ATR analysis and market condition alerts.', price: 23.99, originalPrice: 149.00, featured: false }
  ]

  productTemplates['MT5 Indicators'] = [
    { name: 'Neural Trend Detector', description: 'AI-powered trend detection using neural networks and machine learning algorithms.', price: 39.99, originalPrice: 259.00, featured: true },
    { name: 'Multi-Timeframe RSI', description: 'Advanced RSI indicator with multiple timeframe analysis and divergence detection.', price: 28.99, originalPrice: 189.00, featured: false },
    { name: 'Smart Money Concepts', description: 'Professional SMC indicator with order blocks, fair value gaps, and liquidity analysis.', price: 44.99, originalPrice: 299.00, featured: true },
    { name: 'Harmonic Patterns Pro', description: 'Complete harmonic pattern recognition with Gartley, Butterfly, and Bat patterns.', price: 36.99, originalPrice: 239.00, featured: false },
    { name: 'Market Sentiment', description: 'Real-time market sentiment analysis with COT data and positioning indicators.', price: 32.99, originalPrice: 219.00, featured: false },
    { name: 'Correlation Matrix', description: 'Advanced currency correlation analysis with heat maps and strength meters.', price: 29.99, originalPrice: 199.00, featured: true },
    { name: 'Economic Calendar', description: 'Integrated economic calendar with impact analysis and volatility predictions.', price: 26.99, originalPrice: 179.00, featured: false },
    { name: 'Session Indicator', description: 'Professional trading session indicator with overlap analysis and volume profiles.', price: 24.99, originalPrice: 159.00, featured: false },
    { name: 'Pivot Points Pro', description: 'Advanced pivot point calculator with multiple calculation methods and support/resistance.', price: 21.99, originalPrice: 139.00, featured: true },
    { name: 'Trend Strength Meter', description: 'Multi-currency trend strength analysis with momentum and acceleration indicators.', price: 33.99, originalPrice: 229.00, featured: false }
  ]

  productTemplates['Trading Systems'] = [
    { name: 'Complete Scalping System', description: 'Professional scalping system with entry/exit rules, risk management, and backtesting results.', price: 79.99, originalPrice: 499.00, featured: true },
    { name: 'Swing Trading Masterclass', description: 'Comprehensive swing trading system with position sizing and portfolio management.', price: 69.99, originalPrice: 449.00, featured: false },
    { name: 'News Trading Strategy', description: 'High-impact news trading system with economic calendar integration and volatility analysis.', price: 74.99, originalPrice: 479.00, featured: true },
    { name: 'Grid Trading System', description: 'Advanced grid trading methodology with recovery techniques and risk control measures.', price: 64.99, originalPrice: 419.00, featured: false },
    { name: 'Breakout Trading Pro', description: 'Professional breakout trading system with pattern recognition and momentum confirmation.', price: 72.99, originalPrice: 469.00, featured: false },
    { name: 'Trend Following System', description: 'Complete trend following methodology with multiple timeframe analysis and position management.', price: 67.99, originalPrice: 439.00, featured: true },
    { name: 'Reversal Trading Strategy', description: 'Advanced reversal trading system with divergence analysis and confirmation signals.', price: 71.99, originalPrice: 459.00, featured: false },
    { name: 'Carry Trade System', description: 'Professional carry trade strategy with interest rate analysis and currency selection.', price: 59.99, originalPrice: 389.00, featured: false },
    { name: 'Arbitrage Trading Guide', description: 'Complete arbitrage trading system with multi-broker setup and execution strategies.', price: 84.99, originalPrice: 549.00, featured: true },
    { name: 'Portfolio Management', description: 'Advanced portfolio management system with risk allocation and correlation analysis.', price: 77.99, originalPrice: 499.00, featured: false }
  ]

  // Skip product creation as requested
  console.log('⏭️ Skipping product creation as requested')

  console.log('🎉 Database seed completed successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Seed failed:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
