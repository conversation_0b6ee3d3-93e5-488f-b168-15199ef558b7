import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { exec } from 'child_process'
import { promisify } from 'util'
import path from 'path'
import fs from 'fs/promises'
import { writeFile } from 'fs/promises'

const execAsync = promisify(exec)

export async function POST(request: NextRequest) {
  try {
    // Check if user is admin
    const session = await getServerSession(authOptions)
    if (!session?.user || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('backup') as File
    
    if (!file) {
      return NextResponse.json(
        { message: 'No backup file provided' },
        { status: 400 }
      )
    }

    // Validate file type
    const allowedExtensions = ['.sql', '.db', '.backup']
    const fileExtension = path.extname(file.name).toLowerCase()
    if (!allowedExtensions.includes(fileExtension)) {
      return NextResponse.json(
        { message: 'Invalid file type. Only .sql, .db, and .backup files are allowed' },
        { status: 400 }
      )
    }

    // Create temporary file
    const tempDir = path.join(process.cwd(), 'temp')
    try {
      await fs.access(tempDir)
    } catch {
      await fs.mkdir(tempDir, { recursive: true })
    }

    const tempFilePath = path.join(tempDir, `restore_${Date.now()}_${file.name}`)
    
    try {
      // Save uploaded file to temporary location
      const bytes = await file.arrayBuffer()
      const buffer = Buffer.from(bytes)
      await writeFile(tempFilePath, buffer)

      // Get database URL from environment
      const databaseUrl = process.env.DATABASE_URL
      if (!databaseUrl) {
        return NextResponse.json(
          { message: 'Database URL not configured' },
          { status: 500 }
        )
      }

      // Parse database URL to extract connection details
      const url = new URL(databaseUrl)
      const dbName = url.pathname.slice(1) // Remove leading slash
      const host = url.hostname
      const port = url.port || '5432'
      const username = url.username
      const password = url.password

      // Try different approaches for restore
      try {
        const isWindows = process.platform === 'win32'
        let command: string

        if (isWindows) {
          // Windows command with proper escaping
          command = `set PGPASSWORD=${password} && psql -h ${host} -p ${port} -U ${username} -d ${dbName} -f "${tempFilePath}"`
        } else {
          // Unix/Linux command
          command = `PGPASSWORD="${password}" psql -h ${host} -p ${port} -U ${username} -d ${dbName} -f "${tempFilePath}"`
        }

        await execAsync(command, { maxBuffer: 1024 * 1024 * 10 }) // 10MB buffer

        return NextResponse.json({
          message: 'Database restored successfully',
          filename: file.name
        })
      } catch (error) {
        console.error('Restore error:', error)
        return NextResponse.json(
          { message: 'Failed to restore database', error: error.message },
          { status: 500 }
        )
      }
    } finally {
      // Clean up temporary file
      try {
        await fs.unlink(tempFilePath)
      } catch (error) {
        console.error('Failed to clean up temp file:', error)
      }
    }
  } catch (error) {
    console.error('Database restore error:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}
