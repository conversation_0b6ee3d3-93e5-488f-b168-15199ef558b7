'use client'

import { useEffect, useState } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { toast } from 'react-hot-toast'
import { Button } from '@/components/ui/button'
import { 
  ArrowDownTrayIcon,
  ClockIcon,
  DocumentIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

interface Download {
  id: string
  productId: string
  product: {
    id: string
    name: string
    slug: string
    images: string[]
  }
  token: string
  downloadCount: number
  maxDownloads: number
  expiresAt: string
  isActive: boolean
  createdAt: string
}

export default function DownloadsPage() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [downloads, setDownloads] = useState<Download[]>([])
  const [loading, setLoading] = useState(true)
  const [downloading, setDownloading] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/auth/signin')
      return
    }

    if (status === 'authenticated') {
      fetchDownloads()
    }
  }, [status, router])

  const fetchDownloads = async () => {
    try {
      const response = await fetch('/api/user/downloads')
      const data = await response.json()

      if (data.success) {
        setDownloads(data.data)
      } else {
        toast.error('Failed to load downloads')
      }
    } catch (error) {
      console.error('Error fetching downloads:', error)
      toast.error('Failed to load downloads')
    } finally {
      setLoading(false)
    }
  }

  const handleDownload = async (download: Download) => {
    if (downloading) return

    setDownloading(download.id)
    try {
      const response = await fetch(`/api/downloads/by-token/${download.token}`)
      const data = await response.json()

      if (data.success) {
        // Open download URL in new tab
        window.open(data.data.downloadUrl, '_blank')
        
        // Update download count
        await fetchDownloads()
        
        toast.success(`Download started! ${data.data.remainingDownloads} downloads remaining`)
      } else {
        toast.error(data.message || 'Failed to generate download link')
      }
    } catch (error) {
      console.error('Error downloading file:', error)
      toast.error('Failed to download file')
    } finally {
      setDownloading(null)
    }
  }

  const isExpired = (expiresAt: string) => {
    return new Date(expiresAt) < new Date()
  }

  const getRemainingDownloads = (download: Download) => {
    return Math.max(0, download.maxDownloads - download.downloadCount)
  }

  const getStatusColor = (download: Download) => {
    if (!download.isActive || isExpired(download.expiresAt)) {
      return 'text-red-400'
    }
    if (getRemainingDownloads(download) === 0) {
      return 'text-orange-400'
    }
    return 'text-green-400'
  }

  const getStatusText = (download: Download) => {
    if (!download.isActive) {
      return 'Inactive'
    }
    if (isExpired(download.expiresAt)) {
      return 'Expired'
    }
    if (getRemainingDownloads(download) === 0) {
      return 'Limit Reached'
    }
    return 'Active'
  }

  const canDownload = (download: Download) => {
    return download.isActive && !isExpired(download.expiresAt) && getRemainingDownloads(download) > 0
  }

  if (status === 'loading' || loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-400"></div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">My Downloads</h1>
          <p className="text-gray-300">Access your purchased products and download files</p>
        </div>

        {downloads.length === 0 ? (
          <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-8 text-center">
            <DocumentIcon className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-xl font-semibold text-white mb-2">No Downloads Available</h3>
            <p className="text-gray-400 mb-6">
              You haven't purchased any products yet. Browse our collection to get started.
            </p>
            <Button
              onClick={() => router.push('/products')}
              className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium"
            >
              Browse Products
            </Button>
          </div>
        ) : (
          <div className="space-y-6">
            {downloads.map((download) => (
              <div
                key={download.id}
                className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6"
              >
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    {/* Product Image */}
                    <div className="w-20 h-20 bg-gray-700 rounded-lg overflow-hidden flex-shrink-0">
                      {download.product.images && download.product.images.length > 0 ? (
                        <img
                          src={download.product.images[0]}
                          alt={download.product.name}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <DocumentIcon className="w-8 h-8 text-gray-400" />
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className="flex-1 min-w-0">
                      <h3 className="text-lg font-semibold text-white mb-1">
                        {download.product.name}
                      </h3>
                      
                      <div className="flex items-center space-x-4 text-sm text-gray-400 mb-3">
                        <div className="flex items-center space-x-1">
                          <ArrowDownTrayIcon className="w-4 h-4" />
                          <span>{download.downloadCount} / {download.maxDownloads} downloads used</span>
                        </div>
                        
                        <div className="flex items-center space-x-1">
                          <ClockIcon className="w-4 h-4" />
                          <span>Expires {new Date(download.expiresAt).toLocaleDateString()}</span>
                        </div>
                      </div>

                      {/* Status */}
                      <div className="flex items-center space-x-2">
                        <div className={`flex items-center space-x-1 ${getStatusColor(download)}`}>
                          {canDownload(download) ? (
                            <CheckCircleIcon className="w-4 h-4" />
                          ) : (
                            <ExclamationTriangleIcon className="w-4 h-4" />
                          )}
                          <span className="text-sm font-medium">{getStatusText(download)}</span>
                        </div>
                        
                        {getRemainingDownloads(download) > 0 && canDownload(download) && (
                          <span className="text-sm text-gray-400">
                            • {getRemainingDownloads(download)} downloads remaining
                          </span>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-col space-y-2 ml-4">
                    <Button
                      onClick={() => handleDownload(download)}
                      disabled={!canDownload(download) || downloading === download.id}
                      className={`${
                        canDownload(download)
                          ? 'bg-green-600 hover:bg-green-700 text-white'
                          : 'bg-gray-600 text-gray-400 cursor-not-allowed'
                      }`}
                    >
                      {downloading === download.id ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                          Downloading...
                        </>
                      ) : (
                        <>
                          <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
                          Download
                        </>
                      )}
                    </Button>
                    
                    <Button
                      onClick={() => router.push(`/products/${download.product.slug}`)}
                      variant="outline"
                      size="sm"
                      className="border-gray-600 text-gray-300 hover:bg-gray-700"
                    >
                      View Product
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
