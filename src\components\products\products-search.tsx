'use client'

import { useState, useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { MagnifyingGlassIcon, XMarkIcon } from '@heroicons/react/24/outline'

export function ProductsSearch() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [searchTerm, setSearchTerm] = useState('')

  // Initialize search term from URL
  useEffect(() => {
    const currentSearch = searchParams.get('search') || ''
    setSearchTerm(currentSearch)
  }, [searchParams])

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    
    const params = new URLSearchParams(searchParams.toString())
    
    if (searchTerm.trim()) {
      params.set('search', searchTerm.trim())
    } else {
      params.delete('search')
    }
    
    // Reset to first page when searching
    params.delete('page')
    
    router.push(`/products?${params.toString()}`)
  }

  const clearSearch = () => {
    setSearchTerm('')
    const params = new URLSearchParams(searchParams.toString())
    params.delete('search')
    params.delete('page')
    router.push(`/products?${params.toString()}`)
  }

  return (
    <div className="relative max-w-2xl mx-auto">
      <form onSubmit={handleSearch} className="relative">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
          <Input
            type="text"
            placeholder="Search for Expert Advisors, Indicators, or trading tools..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 pr-20 py-3 bg-white/10 backdrop-blur-md border border-white/20 text-white placeholder-gray-400 focus:border-yellow-500 focus:ring-yellow-500 rounded-xl text-lg"
          />
          {searchTerm && (
            <button
              type="button"
              onClick={clearSearch}
              className="absolute right-16 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-white transition-colors duration-200"
            >
              <XMarkIcon className="w-4 h-4" />
            </button>
          )}
          <Button
            type="submit"
            variant="premium"
            size="sm"
            className="absolute right-2 top-1/2 transform -translate-y-1/2"
          >
            Search
          </Button>
        </div>
      </form>

      {/* Search Suggestions */}
      <div className="mt-4 flex flex-wrap gap-2 justify-center">
        <span className="text-gray-400 text-sm">Popular searches:</span>
        {['Scalping EA', 'Grid Bot', 'Trend Indicator', 'News EA', 'MT5 Robot'].map((suggestion) => (
          <button
            key={suggestion}
            onClick={() => {
              setSearchTerm(suggestion)
              const params = new URLSearchParams(searchParams.toString())
              params.set('search', suggestion)
              params.delete('page')
              router.push(`/products?${params.toString()}`)
            }}
            className="px-3 py-1 bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white text-xs rounded-full transition-colors duration-200"
          >
            {suggestion}
          </button>
        ))}
      </div>
    </div>
  )
}
