import { NextRequest, NextResponse } from 'next/server'
import { sendContactFormNotification } from '@/lib/email'
import { asyncHandler, ValidationError } from '@/lib/error-handler'
import { debug } from '@/lib/debug'
import { z } from 'zod'

const contactFormSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name is too long'),
  email: z.string().email('Invalid email address'),
  subject: z.string().min(1, 'Subject is required').max(200, 'Subject is too long'),
  message: z.string().min(10, 'Message must be at least 10 characters').max(2000, 'Message is too long'),
})

export const POST = asyncHandler(async (request: NextRequest, context) => {
  debug.api('Contact form submission received')

  const body = await request.json()

  // Validate the request body
  try {
    const validatedData = contactFormSchema.parse(body)
    debug.api('Contact form data validated', { email: validatedData.email, subject: validatedData.subject })

    // Send notification email to admin
    await sendContactFormNotification(validatedData)
    debug.email('Contact form notification sent', { to: '<EMAIL>' })

    return NextResponse.json({
      success: true,
      message: 'Your message has been sent successfully. We will get back to you soon!',
      requestId: context?.requestId
    })

  } catch (error) {
    if (error instanceof z.ZodError) {
      debug.warn('VALIDATION', 'Contact form validation failed', error.errors)
      throw new ValidationError('Invalid form data', error.errors)
    }
    throw error
  }
})
