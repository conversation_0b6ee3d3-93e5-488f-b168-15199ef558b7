import { PrismaClient, EmailStatus } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting email logs seed...')

  // Get templates
  const welcomeTemplate = await prisma.emailTemplate.findFirst({
    where: { type: 'WELCOME' }
  })
  
  const passwordResetTemplate = await prisma.emailTemplate.findFirst({
    where: { type: 'PASSWORD_RESET' }
  })

  const orderTemplate = await prisma.emailTemplate.findFirst({
    where: { type: 'ORDER_CONFIRMATION' }
  })

  if (!welcomeTemplate || !passwordResetTemplate || !orderTemplate) {
    console.log('❌ Email templates not found. Please run email templates seed first.')
    return
  }

  // Create sample email logs
  const emailLogs = [
    // Welcome emails
    {
      templateId: welcomeTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Welcome to Forex Bot Zone - Your Trading Journey Starts Here!',
      status: EmailStatus.DELIVERED,
      sentAt: new Date(Date.now() - 86400000 * 5), // 5 days ago
      deliveredAt: new Date(Date.now() - 86400000 * 5 + 30000), // 30 seconds later
      openedAt: new Date(Date.now() - 86400000 * 5 + 3600000), // 1 hour later
    },
    {
      templateId: welcomeTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Welcome to Forex Bot Zone - Your Trading Journey Starts Here!',
      status: EmailStatus.OPENED,
      sentAt: new Date(Date.now() - 86400000 * 4),
      deliveredAt: new Date(Date.now() - 86400000 * 4 + 25000),
      openedAt: new Date(Date.now() - 86400000 * 4 + 7200000), // 2 hours later
    },
    {
      templateId: welcomeTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Welcome to Forex Bot Zone - Your Trading Journey Starts Here!',
      status: EmailStatus.CLICKED,
      sentAt: new Date(Date.now() - 86400000 * 3),
      deliveredAt: new Date(Date.now() - 86400000 * 3 + 20000),
      openedAt: new Date(Date.now() - 86400000 * 3 + 1800000), // 30 minutes later
      clickedAt: new Date(Date.now() - 86400000 * 3 + 1900000), // 10 minutes after opening
    },
    {
      templateId: welcomeTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Welcome to Forex Bot Zone - Your Trading Journey Starts Here!',
      status: EmailStatus.BOUNCED,
      sentAt: new Date(Date.now() - 86400000 * 2),
      bouncedAt: new Date(Date.now() - 86400000 * 2 + 60000),
    },
    {
      templateId: welcomeTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Welcome to Forex Bot Zone - Your Trading Journey Starts Here!',
      status: EmailStatus.DELIVERED,
      sentAt: new Date(Date.now() - 86400000),
      deliveredAt: new Date(Date.now() - 86400000 + 35000),
    },

    // Password reset emails
    {
      templateId: passwordResetTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Reset Your Password - Forex Bot Zone',
      status: EmailStatus.CLICKED,
      sentAt: new Date(Date.now() - 86400000 * 3),
      deliveredAt: new Date(Date.now() - 86400000 * 3 + 15000),
      openedAt: new Date(Date.now() - 86400000 * 3 + 300000), // 5 minutes later
      clickedAt: new Date(Date.now() - 86400000 * 3 + 360000), // 1 minute after opening
    },
    {
      templateId: passwordResetTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Reset Your Password - Forex Bot Zone',
      status: EmailStatus.OPENED,
      sentAt: new Date(Date.now() - 86400000 * 2),
      deliveredAt: new Date(Date.now() - 86400000 * 2 + 18000),
      openedAt: new Date(Date.now() - 86400000 * 2 + 1200000), // 20 minutes later
    },
    {
      templateId: passwordResetTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Reset Your Password - Forex Bot Zone',
      status: EmailStatus.FAILED,
      sentAt: new Date(Date.now() - 86400000),
      failedAt: new Date(Date.now() - 86400000 + 5000),
      errorMessage: 'SMTP connection failed'
    },

    // Order confirmation emails
    {
      templateId: orderTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Order Confirmation - Your Purchase is Complete!',
      status: EmailStatus.CLICKED,
      sentAt: new Date(Date.now() - 86400000 * 4),
      deliveredAt: new Date(Date.now() - 86400000 * 4 + 12000),
      openedAt: new Date(Date.now() - 86400000 * 4 + 600000), // 10 minutes later
      clickedAt: new Date(Date.now() - 86400000 * 4 + 720000), // 2 minutes after opening
    },
    {
      templateId: orderTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Order Confirmation - Your Purchase is Complete!',
      status: EmailStatus.DELIVERED,
      sentAt: new Date(Date.now() - 86400000 * 2),
      deliveredAt: new Date(Date.now() - 86400000 * 2 + 22000),
    },
    {
      templateId: orderTemplate.id,
      to: '<EMAIL>',
      from: 'Forex Bot Zone <<EMAIL>>',
      subject: 'Order Confirmation - Your Purchase is Complete!',
      status: EmailStatus.OPENED,
      sentAt: new Date(Date.now() - 86400000),
      deliveredAt: new Date(Date.now() - 86400000 + 28000),
      openedAt: new Date(Date.now() - 86400000 + 3600000), // 1 hour later
    }
  ]

  for (const log of emailLogs) {
    await prisma.emailLog.create({
      data: log
    })
  }

  console.log(`✅ Created ${emailLogs.length} sample email logs`)
  console.log('🎉 Email logs seed completed successfully!')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error('❌ Email logs seed failed:', e)
    await prisma.$disconnect()
    process.exit(1)
  })
