import { Metadata } from 'next'
import Link from 'next/link'
import Image from 'next/image'
import { prisma } from '@/lib/prisma'
import { Button } from '@/components/ui/button'
import { 
  ChartBarIcon, 
  CogIcon, 
  TrophyIcon,
  ArrowRightIcon 
} from '@heroicons/react/24/outline'

export const metadata: Metadata = {
  title: 'Trading Categories - Forex Bot Zone',
  description: 'Browse our categories of premium Forex Expert Advisors and Indicators. Find the perfect trading tools for your strategy.',
}

async function getCategories() {
  const categories = await prisma.category.findMany({
    where: { isActive: true },
    include: {
      _count: {
        select: {
          products: {
            where: { status: 'PUBLISHED' }
          }
        }
      }
    },
    orderBy: [
      { sortOrder: 'asc' },
      { name: 'asc' }
    ]
  })

  return categories
}

const categoryIcons = {
  'Expert Advisors': CogIcon,
  'Indicators': ChartBarIcon,
  'Trading Tools': TrophyIcon,
}

export default async function CategoriesPage() {
  const categories = await getCategories()

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-gray-800 to-black pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            Trading Categories
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Explore our premium collection of Forex trading tools organized by category. 
            Find exactly what you need to enhance your trading strategy.
          </p>
        </div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {categories.map((category) => {
            const IconComponent = categoryIcons[category.name as keyof typeof categoryIcons] || ChartBarIcon
            
            return (
              <div
                key={category.id}
                className="group relative bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-8 hover:border-yellow-500/30 hover:shadow-glow transition-all duration-300"
              >
                {/* Icon */}
                <div className="w-16 h-16 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                  <IconComponent className="w-8 h-8 text-black" />
                </div>

                {/* Content */}
                <h3 className="text-2xl font-bold text-white mb-3 group-hover:text-yellow-400 transition-colors duration-300">
                  {category.name}
                </h3>
                
                {category.description && (
                  <p className="text-gray-300 mb-4 line-clamp-3">
                    {category.description}
                  </p>
                )}

                <div className="flex items-center justify-between mb-6">
                  <span className="text-sm text-gray-400">
                    {category._count.products} products
                  </span>
                  <span className="px-3 py-1 bg-yellow-500/10 border border-yellow-500/20 rounded-full text-yellow-400 text-xs font-medium">
                    Special Prices
                  </span>
                </div>

                {/* CTA Button */}
                <Link href={`/products?category=${category.slug}`}>
                  <Button variant="ghost" className="w-full group-hover:bg-yellow-500/10 group-hover:text-yellow-400 group-hover:border-yellow-500/30">
                    Browse {category.name}
                    <ArrowRightIcon className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform duration-200" />
                  </Button>
                </Link>

                {/* Hover Effect */}
                <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/5 to-orange-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
              </div>
            )
          })}
        </div>

        {/* Featured Section */}
        <div className="mt-16 text-center">
          <div className="bg-gradient-to-r from-yellow-500/10 to-orange-500/10 border border-yellow-500/20 rounded-2xl p-8">
            <TrophyIcon className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
            <h2 className="text-3xl font-bold text-white mb-4">
              Can't Find What You're Looking For?
            </h2>
            <p className="text-gray-300 mb-6 max-w-2xl mx-auto">
              Our team is constantly adding new premium trading tools. 
              Contact us if you need a specific Expert Advisor or Indicator.
            </p>
            <Link href="/contact">
              <Button variant="premium" size="lg">
                Contact Us
              </Button>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
