'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  CogIcon,
  ShieldCheckIcon,
  CurrencyDollarIcon,
  EnvelopeIcon,
  GlobeAltIcon,
  CloudArrowUpIcon,
  CircleStackIcon,
  ArrowDownTrayIcon,
  ArrowUpTrayIcon
} from '@heroicons/react/24/outline'
import { toast } from 'react-hot-toast'

const tabs = [
  { id: 'general', name: 'General', icon: GlobeAltIcon },
  { id: 'ecommerce', name: 'E-commerce', icon: CurrencyDollarIcon },
  { id: 'files', name: 'File Upload', icon: CloudArrowUpIcon },
  { id: 'system', name: 'System', icon: ShieldCheckIcon },
  { id: 'database', name: 'Database', icon: CircleStackIcon },
]

export function AdminSettings() {
  const [activeTab, setActiveTab] = useState('general')
  const [settings, setSettings] = useState({
    siteName: 'Forex Bot Zone',
    siteDescription: 'Premium Forex Expert Advisors and Indicators at Special Prices',
    contactEmail: '<EMAIL>',
    currency: 'USD',
    taxRate: '0',
    enableRegistration: true,
    enableGuestCheckout: true,
    maintenanceMode: false,
    emailNotifications: true,
    orderNotifications: true,
    maxFileSize: '50',
    allowedFileTypes: '.ex4,.ex5,.mq4,.mq5,.pdf,.txt',
  })

  const [loading, setLoading] = useState(false)
  const [backupLoading, setBackupLoading] = useState(false)
  const [restoreLoading, setRestoreLoading] = useState(false)

  const handleSave = async () => {
    setLoading(true)
    try {
      // API call to save settings would go here
      await new Promise(resolve => setTimeout(resolve, 1000)) // Mock delay
      console.log('Settings saved:', settings)
      toast.success('Settings saved successfully!')
    } catch (error) {
      console.error('Error saving settings:', error)
      toast.error('Failed to save settings')
    } finally {
      setLoading(false)
    }
  }

  const handleInputChange = (key: string, value: string | boolean) => {
    setSettings(prev => ({ ...prev, [key]: value }))
  }

  const handleBackupDatabase = async () => {
    setBackupLoading(true)
    try {
      const response = await fetch('/api/admin/database/backup', {
        method: 'POST',
      })

      if (response.ok) {
        const data = await response.json()
        toast.success(`Database backup created: ${data.filename}`)
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to create backup')
      }
    } catch (error) {
      console.error('Error creating backup:', error)
      toast.error('Failed to create database backup')
    } finally {
      setBackupLoading(false)
    }
  }

  const handleRestoreDatabase = async (file: File) => {
    setRestoreLoading(true)
    try {
      const formData = new FormData()
      formData.append('backup', file)

      const response = await fetch('/api/admin/database/restore', {
        method: 'POST',
        body: formData,
      })

      if (response.ok) {
        toast.success('Database restored successfully!')
      } else {
        const error = await response.json()
        toast.error(error.message || 'Failed to restore database')
      }
    } catch (error) {
      console.error('Error restoring database:', error)
      toast.error('Failed to restore database')
    } finally {
      setRestoreLoading(false)
    }
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="siteName" className="text-white">Site Name</Label>
                <Input
                  id="siteName"
                  value={settings.siteName}
                  onChange={(e) => handleInputChange('siteName', e.target.value)}
                  className="mt-1 bg-white/10 border-white/20 text-white"
                />
              </div>

              <div>
                <Label htmlFor="contactEmail" className="text-white">Contact Email</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  value={settings.contactEmail}
                  onChange={(e) => handleInputChange('contactEmail', e.target.value)}
                  className="mt-1 bg-white/10 border-white/20 text-white"
                />
              </div>

              <div className="md:col-span-2">
                <Label htmlFor="siteDescription" className="text-white">Site Description</Label>
                <textarea
                  id="siteDescription"
                  value={settings.siteDescription}
                  onChange={(e) => handleInputChange('siteDescription', e.target.value)}
                  rows={3}
                  className="mt-1 w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        )

      case 'ecommerce':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="currency" className="text-white">Default Currency</Label>
                <select
                  id="currency"
                  value={settings.currency}
                  onChange={(e) => handleInputChange('currency', e.target.value)}
                  className="mt-1 w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-yellow-400"
                >
                  <option value="USD">USD - US Dollar</option>
                  <option value="EUR">EUR - Euro</option>
                  <option value="GBP">GBP - British Pound</option>
                  <option value="JPY">JPY - Japanese Yen</option>
                </select>
              </div>

              <div>
                <Label htmlFor="taxRate" className="text-white">Tax Rate (%)</Label>
                <Input
                  id="taxRate"
                  type="number"
                  value={settings.taxRate}
                  onChange={(e) => handleInputChange('taxRate', e.target.value)}
                  className="mt-1 bg-white/10 border-white/20 text-white"
                  min="0"
                  max="100"
                  step="0.01"
                />
              </div>

              <div className="flex items-center">
                <input
                  id="enableRegistration"
                  type="checkbox"
                  checked={settings.enableRegistration}
                  onChange={(e) => handleInputChange('enableRegistration', e.target.checked)}
                  className="h-4 w-4 text-yellow-400 focus:ring-yellow-400 border-gray-300 rounded"
                />
                <Label htmlFor="enableRegistration" className="ml-2 text-white">Enable User Registration</Label>
              </div>

              <div className="flex items-center">
                <input
                  id="enableGuestCheckout"
                  type="checkbox"
                  checked={settings.enableGuestCheckout}
                  onChange={(e) => handleInputChange('enableGuestCheckout', e.target.checked)}
                  className="h-4 w-4 text-yellow-400 focus:ring-yellow-400 border-gray-300 rounded"
                />
                <Label htmlFor="enableGuestCheckout" className="ml-2 text-white">Enable Guest Checkout</Label>
              </div>
            </div>
          </div>
        )

      case 'files':
        return (
          <div className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label htmlFor="maxFileSize" className="text-white">Max File Size (MB)</Label>
                <Input
                  id="maxFileSize"
                  type="number"
                  value={settings.maxFileSize}
                  onChange={(e) => handleInputChange('maxFileSize', e.target.value)}
                  className="mt-1 bg-white/10 border-white/20 text-white"
                  min="1"
                  max="1000"
                />
              </div>

              <div>
                <Label htmlFor="allowedFileTypes" className="text-white">Allowed File Types</Label>
                <Input
                  id="allowedFileTypes"
                  value={settings.allowedFileTypes}
                  onChange={(e) => handleInputChange('allowedFileTypes', e.target.value)}
                  className="mt-1 bg-white/10 border-white/20 text-white"
                  placeholder=".ex4,.ex5,.mq4,.mq5,.pdf"
                />
              </div>
            </div>
          </div>
        )

      case 'system':
        return (
          <div className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center">
                <input
                  id="maintenanceMode"
                  type="checkbox"
                  checked={settings.maintenanceMode}
                  onChange={(e) => handleInputChange('maintenanceMode', e.target.checked)}
                  className="h-4 w-4 text-yellow-400 focus:ring-yellow-400 border-gray-300 rounded"
                />
                <Label htmlFor="maintenanceMode" className="ml-2 text-white">Maintenance Mode</Label>
              </div>

              <div className="flex items-center">
                <input
                  id="emailNotifications"
                  type="checkbox"
                  checked={settings.emailNotifications}
                  onChange={(e) => handleInputChange('emailNotifications', e.target.checked)}
                  className="h-4 w-4 text-yellow-400 focus:ring-yellow-400 border-gray-300 rounded"
                />
                <Label htmlFor="emailNotifications" className="ml-2 text-white">Email Notifications</Label>
              </div>

              <div className="flex items-center">
                <input
                  id="orderNotifications"
                  type="checkbox"
                  checked={settings.orderNotifications}
                  onChange={(e) => handleInputChange('orderNotifications', e.target.checked)}
                  className="h-4 w-4 text-yellow-400 focus:ring-yellow-400 border-gray-300 rounded"
                />
                <Label htmlFor="orderNotifications" className="ml-2 text-white">Order Notifications</Label>
              </div>
            </div>
          </div>
        )

      case 'database':
        return (
          <div className="space-y-6">
            <div className="bg-yellow-500/10 border border-yellow-500/20 rounded-lg p-4 mb-6">
              <div className="flex items-start">
                <div className="flex-shrink-0">
                  <ShieldCheckIcon className="h-5 w-5 text-yellow-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-yellow-400">Important Notice</h3>
                  <div className="mt-2 text-sm text-yellow-300">
                    <p>Database operations are critical. Always ensure you have recent backups before performing any restore operations.</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Backup Section */}
              <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
                <h4 className="text-lg font-medium text-white mb-4 flex items-center">
                  <ArrowDownTrayIcon className="h-5 w-5 mr-2 text-green-400" />
                  Create Backup
                </h4>
                <p className="text-gray-400 text-sm mb-4">
                  Create a complete backup of your database. The backup will be saved to the /Backup folder.
                </p>
                <Button
                  onClick={handleBackupDatabase}
                  disabled={backupLoading}
                  className="w-full bg-green-600 hover:bg-green-700 text-white"
                >
                  {backupLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                      Creating Backup...
                    </>
                  ) : (
                    <>
                      <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                      Create Database Backup
                    </>
                  )}
                </Button>
              </div>

              {/* Restore Section */}
              <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
                <h4 className="text-lg font-medium text-white mb-4 flex items-center">
                  <ArrowUpTrayIcon className="h-5 w-5 mr-2 text-orange-400" />
                  Restore Database
                </h4>
                <p className="text-gray-400 text-sm mb-4">
                  Restore your database from a backup file. This will replace all current data.
                </p>
                <div className="space-y-3">
                  <input
                    type="file"
                    accept=".sql,.db,.backup"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) {
                        if (confirm('Are you sure you want to restore the database? This will replace all current data.')) {
                          handleRestoreDatabase(file)
                        }
                      }
                    }}
                    disabled={restoreLoading}
                    className="w-full text-sm text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-orange-600 file:text-white hover:file:bg-orange-700 file:disabled:opacity-50"
                  />
                  {restoreLoading && (
                    <div className="flex items-center text-orange-400 text-sm">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-400 mr-2"></div>
                      Restoring database...
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* Backup History */}
            <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl p-6">
              <h4 className="text-lg font-medium text-white mb-4 flex items-center">
                <CircleStackIcon className="h-5 w-5 mr-2 text-blue-400" />
                Recent Backups
              </h4>
              <div className="text-gray-400 text-sm">
                <p>Backup files are stored in the <code className="bg-gray-800 px-2 py-1 rounded text-yellow-400">/Backup</code> folder.</p>
                <p className="mt-2">Backup files are named with timestamp: <code className="bg-gray-800 px-2 py-1 rounded text-yellow-400">backup_YYYY-MM-DD_HH-MM-SS.sql</code></p>
              </div>
            </div>
          </div>
        )

      default:
        return null
    }
  }

  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="bg-white/5 backdrop-blur-md border border-white/10 rounded-xl">
        <div className="border-b border-white/10">
          <nav className="flex space-x-8 px-6" aria-label="Tabs">
            {tabs.map((tab) => {
              const Icon = tab.icon
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'border-yellow-400 text-yellow-400'
                      : 'border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-5 w-5 mr-2" />
                  {tab.name}
                </button>
              )
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="p-6">
          {renderTabContent()}
        </div>
      </div>

      {/* Save Button - Only show for non-database tabs */}
      {activeTab !== 'database' && (
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={loading}
            className="bg-yellow-400 hover:bg-yellow-500 text-black font-medium px-8 py-2"
          >
            {loading ? 'Saving...' : 'Save Settings'}
          </Button>
        </div>
      )}
    </div>
  )
}
