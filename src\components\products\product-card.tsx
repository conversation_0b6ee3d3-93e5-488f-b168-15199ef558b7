'use client'

import { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { Button } from '@/components/ui/button'
import { useCartStore } from '@/store/cart-store'
import { ProductWithRelations } from '@/types'
import { formatCurrency } from '@/lib/utils'
import {
  HeartIcon,
  ShoppingCartIcon,
  StarIcon,
  EyeIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline'
import { HeartIcon as HeartSolidIcon } from '@heroicons/react/24/solid'
import toast from 'react-hot-toast'

interface ProductCardProps {
  product: ProductWithRelations
  className?: string
}

export function ProductCard({ product, className = '' }: ProductCardProps) {
  const [isWishlisted, setIsWishlisted] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { addItem, hasItem, openCart } = useCartStore()

  const effectivePrice = product.isOnSale && product.salePrice
    ? product.salePrice
    : product.price

  const discountPercentage = product.isOnSale && product.originalPrice && product.salePrice
    ? Math.round(((product.originalPrice - product.salePrice) / product.originalPrice) * 100)
    : 0

  const isInCart = hasItem(product.id)

  const handleAddToCart = async () => {
    setIsLoading(true)
    try {
      addItem(product)
      toast.success('Added to cart!')
      openCart()
    } catch (error) {
      toast.error('Failed to add to cart')
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleWishlist = () => {
    setIsWishlisted(!isWishlisted)
    toast.success(isWishlisted ? 'Removed from wishlist' : 'Added to wishlist')
  }

  return (
    <div className={`group relative bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl overflow-hidden hover:border-yellow-500/50 transition-all duration-300 hover:shadow-2xl hover:shadow-yellow-500/10 ${className}`}>
      {/* Sale Badge */}
      {product.isOnSale && discountPercentage > 0 && (
        <div className="absolute top-2 left-2 sm:top-3 sm:left-3 z-10 bg-gradient-to-r from-red-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-semibold">
          -{discountPercentage}%
        </div>
      )}

      {/* Featured Badge */}
      {product.featured && (
        <div className="absolute top-2 right-2 sm:top-3 sm:right-3 z-10 bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-2 py-1 rounded-full text-xs font-semibold">
          Featured
        </div>
      )}

      {/* Product Image */}
      <div className="relative aspect-video overflow-hidden bg-gray-800">
        {product.images && product.images.length > 0 ? (
          <Image
            src={product.images[0]}
            alt={product.name}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-700 to-gray-800">
            <ArrowDownTrayIcon className="w-12 h-12 text-gray-500" />
          </div>
        )}

        {/* Overlay Actions */}
        <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center space-x-3">
          <Link href={`/products/${product.slug}`}>
            <Button variant="glass" size="sm">
              <EyeIcon className="w-4 h-4 mr-2" />
              View
            </Button>
          </Link>
          <Button
            variant="glass"
            size="sm"
            onClick={handleToggleWishlist}
          >
            {isWishlisted ? (
              <HeartSolidIcon className="w-4 h-4 text-red-500" />
            ) : (
              <HeartIcon className="w-4 h-4" />
            )}
          </Button>
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4 sm:p-6">
        {/* Category */}
        <div className="flex items-center justify-between mb-2">
          <span className="text-xs text-yellow-400 font-medium uppercase tracking-wide">
            {product.category.name}
          </span>

          {/* Rating */}
          <div className="flex items-center space-x-1">
            <StarIcon className="w-3 h-3 sm:w-4 sm:h-4 text-yellow-400 fill-current" />
            <span className="text-xs sm:text-sm text-gray-300">
              {product.averageRating?.toFixed(1) || '0.0'}
            </span>
            <span className="text-xs text-gray-500 hidden sm:inline">
              ({product._count?.reviews || 0})
            </span>
          </div>
        </div>

        {/* Product Name */}
        <Link href={`/products/${product.slug}`}>
          <h3 className="text-base sm:text-lg font-semibold text-white mb-2 line-clamp-2 hover:text-yellow-400 transition-colors duration-200">
            {product.name}
          </h3>
        </Link>

        {/* Description */}
        {product.shortDescription && (
          <p className="text-gray-400 text-sm mb-4 line-clamp-2">
            {product.shortDescription}
          </p>
        )}

        {/* Tags */}
        {product.tags && product.tags.length > 0 && (
          <div className="flex flex-wrap gap-1 mb-4">
            {product.tags.slice(0, 3).map((tag) => (
              <span
                key={tag}
                className="px-2 py-1 bg-gray-800 text-gray-300 text-xs rounded-full"
              >
                {tag}
              </span>
            ))}
            {product.tags.length > 3 && (
              <span className="px-2 py-1 bg-gray-800 text-gray-300 text-xs rounded-full">
                +{product.tags.length - 3}
              </span>
            )}
          </div>
        )}

        {/* Price and Actions */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-xl font-bold text-white">
              {formatCurrency(effectivePrice)}
            </span>
            {product.isOnSale && product.originalPrice && (
              <span className="text-sm text-gray-500 line-through">
                {formatCurrency(product.originalPrice)}
              </span>
            )}
          </div>

          <Button
            onClick={handleAddToCart}
            disabled={isLoading || isInCart}
            variant={isInCart ? "secondary" : "premium"}
            size="sm"
            className="min-w-[100px]"
          >
            {isLoading ? (
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
            ) : isInCart ? (
              <>
                <ShoppingCartIcon className="w-4 h-4 mr-2" />
                In Cart
              </>
            ) : (
              <>
                <ShoppingCartIcon className="w-4 h-4 mr-2" />
                Add to Cart
              </>
            )}
          </Button>
        </div>


      </div>

      {/* Premium Shine Effect */}
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000 pointer-events-none" />
    </div>
  )
}
