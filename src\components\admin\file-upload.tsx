'use client'

import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { 
  CloudArrowUpIcon, 
  DocumentIcon, 
  TrashIcon,
  CheckCircleIcon 
} from '@heroicons/react/24/outline'
import toast from 'react-hot-toast'

interface FileUploadProps {
  productId: string
  currentFile?: {
    fileName: string
    fileSize: number
    fileKey: string
  }
  onUploadSuccess?: (fileInfo: any) => void
  className?: string
}

export function FileUpload({ 
  productId, 
  currentFile, 
  onUploadSuccess, 
  className = '' 
}: FileUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const allowedTypes = ['.ex4', '.ex5', '.mq4', '.mq5', '.zip', '.rar', '.pdf', '.txt']
  const maxSize = 100 * 1024 * 1024 // 100MB

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const validateFile = (file: File): string | null => {
    const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`
    
    if (!allowedTypes.includes(fileExtension)) {
      return `File type ${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
    }

    if (file.size > maxSize) {
      return `File size exceeds maximum limit of ${formatFileSize(maxSize)}`
    }

    return null
  }

  const uploadFile = async (file: File) => {
    const validationError = validateFile(file)
    if (validationError) {
      toast.error(validationError)
      return
    }

    setUploading(true)

    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('productId', productId)
      formData.append('fileType', 'product')

      const response = await fetch('/api/admin/upload', {
        method: 'POST',
        body: formData,
      })

      const data = await response.json()

      if (data.success) {
        toast.success('File uploaded successfully!')
        onUploadSuccess?.(data.data)
      } else {
        toast.error(data.message || 'Failed to upload file')
      }
    } catch (error) {
      console.error('Error uploading file:', error)
      toast.error('Failed to upload file')
    } finally {
      setUploading(false)
    }
  }

  const handleFileSelect = (files: FileList | null) => {
    if (!files || files.length === 0) return

    const file = files[0]
    uploadFile(file)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div>
        <Label className="text-white">Product File</Label>
        <p className="text-sm text-gray-400 mt-1">
          Upload the main product file (EA, Indicator, etc.)
        </p>
      </div>

      {/* Current File Display */}
      {currentFile && (
        <div className="bg-green-900/20 border border-green-500/30 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <CheckCircleIcon className="h-5 w-5 text-green-400" />
              <div>
                <p className="text-sm font-medium text-white">{currentFile.fileName}</p>
                <p className="text-xs text-gray-400">
                  {formatFileSize(Number(currentFile.fileSize))}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              className="text-red-400 hover:text-red-300"
              onClick={() => {
                // TODO: Implement file deletion
                toast.info('File deletion not implemented yet')
              }}
            >
              <TrashIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Upload Area */}
      <div
        className={`
          border-2 border-dashed rounded-lg p-8 text-center transition-colors
          ${dragOver 
            ? 'border-yellow-400 bg-yellow-400/10' 
            : 'border-gray-600 hover:border-gray-500'
          }
          ${uploading ? 'opacity-50 pointer-events-none' : ''}
        `}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={allowedTypes.join(',')}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
        />

        {uploading ? (
          <div className="space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-yellow-400 mx-auto"></div>
            <p className="text-white">Uploading file...</p>
          </div>
        ) : (
          <div className="space-y-4">
            <CloudArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto" />
            <div>
              <p className="text-white mb-2">
                Drag and drop your file here, or{' '}
                <button
                  type="button"
                  onClick={() => fileInputRef.current?.click()}
                  className="text-yellow-400 hover:text-yellow-300 underline"
                >
                  browse
                </button>
              </p>
              <p className="text-sm text-gray-400">
                Supported formats: {allowedTypes.join(', ')}
              </p>
              <p className="text-sm text-gray-400">
                Maximum size: {formatFileSize(maxSize)}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Manual Upload Button */}
      <Button
        type="button"
        variant="outline"
        onClick={() => fileInputRef.current?.click()}
        disabled={uploading}
        className="w-full"
      >
        <DocumentIcon className="h-4 w-4 mr-2" />
        {currentFile ? 'Replace File' : 'Select File'}
      </Button>
    </div>
  )
}
