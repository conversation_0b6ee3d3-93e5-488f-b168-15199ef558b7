import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/user/downloads - Get user's download tokens
export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    
    if (!session?.user?.id) {
      return NextResponse.json({
        success: false,
        message: 'Unauthorized'
      }, { status: 401 })
    }

    const downloads = await prisma.downloadToken.findMany({
      where: { 
        userId: session.user.id,
        isActive: true
      },
      include: {
        product: {
          select: {
            id: true,
            name: true,
            slug: true,
            images: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Convert BigInt to number for JSON serialization
    const serializedDownloads = downloads.map(download => ({
      ...download,
      downloadCount: Number(download.downloadCount),
      maxDownloads: Number(download.maxDownloads)
    }))

    return NextResponse.json({
      success: true,
      data: serializedDownloads
    })

  } catch (error) {
    console.error('Error fetching user downloads:', error)
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 })
  }
}
