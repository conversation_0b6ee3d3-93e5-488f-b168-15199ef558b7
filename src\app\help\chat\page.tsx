'use client'

import { useState, useRef, useEffect } from 'react'
import Link from 'next/link'
import { 
  ArrowLeftIcon,
  PaperAirplaneIcon,
  UserIcon,
  ChatBubbleLeftRightIcon,
  ClockIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline'

interface Message {
  id: string
  text: string
  sender: 'user' | 'support'
  timestamp: Date
}

export default function LiveChatPage() {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      text: 'Hello! Welcome to BotZone support. How can I help you today?',
      sender: 'support',
      timestamp: new Date()
    }
  ])
  const [newMessage, setNewMessage] = useState('')
  const [isConnected, setIsConnected] = useState(true)
  const [isTyping, setIsTyping] = useState(false)
  const messagesEndRef = useRef<HTMLDivElement>(null)

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  const sendMessage = () => {
    if (!newMessage.trim()) return

    const userMessage: Message = {
      id: Date.now().toString(),
      text: newMessage,
      sender: 'user',
      timestamp: new Date()
    }

    setMessages(prev => [...prev, userMessage])
    setNewMessage('')
    setIsTyping(true)

    // Simulate support response
    setTimeout(() => {
      const supportMessage: Message = {
        id: (Date.now() + 1).toString(),
        text: 'Thank you for your message. I\'m looking into this for you. In the meantime, you can also check our FAQ section or contact us via <NAME_EMAIL> for detailed assistance.',
        sender: 'support',
        timestamp: new Date()
      }
      setMessages(prev => [...prev, supportMessage])
      setIsTyping(false)
    }, 2000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const quickQuestions = [
    'How do I install an Expert Advisor?',
    'I need help with my download',
    'My EA is not working properly',
    'I want to request a refund',
    'How do I optimize EA settings?'
  ]

  return (
    <div className="min-h-screen py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <Link 
            href="/help" 
            className="inline-flex items-center text-gray-400 hover:text-white transition-colors duration-200 mb-4"
          >
            <ArrowLeftIcon className="w-4 h-4 mr-2" />
            Back to Help Center
          </Link>
          <h1 className="text-4xl font-bold text-white mb-4">Live Chat Support</h1>
          <p className="text-xl text-gray-300">
            Get instant help from our support team
          </p>
        </div>

        {/* Chat Status */}
        <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <div className={`w-3 h-3 rounded-full mr-3 ${isConnected ? 'bg-green-400' : 'bg-red-400'}`}></div>
              <span className="text-white font-medium">
                {isConnected ? 'Connected to Support' : 'Disconnected'}
              </span>
            </div>
            <div className="flex items-center text-gray-400 text-sm">
              <ClockIcon className="w-4 h-4 mr-1" />
              Average response time: 2 minutes
            </div>
          </div>
        </div>

        {/* Chat Container */}
        <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl overflow-hidden">
          {/* Chat Header */}
          <div className="bg-gray-800/50 px-6 py-4 border-b border-gray-700/50">
            <div className="flex items-center">
              <ChatBubbleLeftRightIcon className="w-6 h-6 text-yellow-400 mr-3" />
              <div>
                <h3 className="text-white font-semibold">Support Chat</h3>
                <p className="text-gray-400 text-sm">We're here to help you</p>
              </div>
            </div>
          </div>

          {/* Messages */}
          <div className="h-96 overflow-y-auto p-6 space-y-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex items-start max-w-xs lg:max-w-md ${message.sender === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                  <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${message.sender === 'user' ? 'bg-yellow-400 text-black ml-3' : 'bg-gray-700 text-gray-300 mr-3'}`}>
                    {message.sender === 'user' ? (
                      <UserIcon className="w-4 h-4" />
                    ) : (
                      <ChatBubbleLeftRightIcon className="w-4 h-4" />
                    )}
                  </div>
                  <div className={`rounded-lg px-4 py-2 ${message.sender === 'user' ? 'bg-yellow-400 text-black' : 'bg-gray-700 text-white'}`}>
                    <p className="text-sm">{message.text}</p>
                    <p className={`text-xs mt-1 ${message.sender === 'user' ? 'text-black/70' : 'text-gray-400'}`}>
                      {message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                  </div>
                </div>
              </div>
            ))}
            
            {/* Typing Indicator */}
            {isTyping && (
              <div className="flex justify-start">
                <div className="flex items-start max-w-xs lg:max-w-md">
                  <div className="flex-shrink-0 w-8 h-8 rounded-full bg-gray-700 text-gray-300 mr-3 flex items-center justify-center">
                    <ChatBubbleLeftRightIcon className="w-4 h-4" />
                  </div>
                  <div className="bg-gray-700 text-white rounded-lg px-4 py-2">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                      <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
            <div ref={messagesEndRef} />
          </div>

          {/* Quick Questions */}
          <div className="px-6 py-4 border-t border-gray-700/50">
            <p className="text-gray-400 text-sm mb-3">Quick questions:</p>
            <div className="flex flex-wrap gap-2">
              {quickQuestions.map((question, index) => (
                <button
                  key={index}
                  onClick={() => setNewMessage(question)}
                  className="text-xs bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-1 rounded-full transition-colors duration-200"
                >
                  {question}
                </button>
              ))}
            </div>
          </div>

          {/* Message Input */}
          <div className="px-6 py-4 border-t border-gray-700/50">
            <div className="flex items-center space-x-3">
              <div className="flex-1">
                <textarea
                  value={newMessage}
                  onChange={(e) => setNewMessage(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message..."
                  className="w-full bg-gray-800 border border-gray-600 rounded-lg px-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:border-yellow-400 resize-none"
                  rows={1}
                />
              </div>
              <button
                onClick={sendMessage}
                disabled={!newMessage.trim()}
                className="bg-yellow-400 hover:bg-yellow-500 disabled:bg-gray-600 disabled:cursor-not-allowed text-black p-2 rounded-lg transition-colors duration-200"
              >
                <PaperAirplaneIcon className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Support Info */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-3">Support Hours</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-300">Monday - Friday:</span>
                <span className="text-white">9:00 AM - 6:00 PM EST</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Saturday:</span>
                <span className="text-white">10:00 AM - 4:00 PM EST</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-300">Sunday:</span>
                <span className="text-white">Closed</span>
              </div>
            </div>
          </div>

          <div className="bg-gray-900/50 backdrop-blur-sm border border-gray-700/50 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-3">Other Ways to Reach Us</h3>
            <div className="space-y-3">
              <Link href="/contact" className="flex items-center text-gray-300 hover:text-yellow-400 transition-colors duration-200">
                <CheckCircleIcon className="w-4 h-4 mr-2" />
                Contact Form
              </Link>
              <a href="mailto:<EMAIL>" className="flex items-center text-gray-300 hover:text-yellow-400 transition-colors duration-200">
                <CheckCircleIcon className="w-4 h-4 mr-2" />
                <EMAIL>
              </a>
              <Link href="/help/faq" className="flex items-center text-gray-300 hover:text-yellow-400 transition-colors duration-200">
                <CheckCircleIcon className="w-4 h-4 mr-2" />
                FAQ Section
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
