import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth-utils'
import { uploadFile, generateFileKey, ALLOWED_FILE_TYPES, MAX_FILE_SIZES } from '@/lib/cloudflare-r2'
import { prisma } from '@/lib/prisma'

// POST /api/admin/upload - Upload files to R2
export async function POST(request: NextRequest) {
  try {
    await requireAdmin()

    const formData = await request.formData()
    const file = formData.get('file') as File
    const productId = formData.get('productId') as string
    const type = formData.get('type') as string || 'product-file'

    if (!file) {
      return NextResponse.json({
        success: false,
        message: 'No file provided'
      }, { status: 400 })
    }

    // For product files, productId is optional during product creation
    // Files can be uploaded before product creation and associated later

    // Validate file type and size based on upload type
    let allowedTypes: string[]
    let maxFileSize: number

    if (type === 'product-image') {
      allowedTypes = ALLOWED_FILE_TYPES.IMAGES || ['.jpg', '.jpeg', '.png', '.gif', '.webp']
      maxFileSize = MAX_FILE_SIZES.IMAGE || 5 * 1024 * 1024 // 5MB
    } else {
      allowedTypes = ALLOWED_FILE_TYPES.PRODUCT_FILES
      maxFileSize = MAX_FILE_SIZES.PRODUCT_FILE
    }

    const fileExtension = `.${file.name.split('.').pop()?.toLowerCase()}`

    if (!allowedTypes.includes(fileExtension)) {
      return NextResponse.json({
        success: false,
        message: `File type ${fileExtension} is not allowed. Allowed types: ${allowedTypes.join(', ')}`
      }, { status: 400 })
    }

    // Validate file size
    if (file.size > maxFileSize) {
      return NextResponse.json({
        success: false,
        message: `File size exceeds maximum limit of ${maxFileSize / (1024 * 1024)}MB`
      }, { status: 400 })
    }

    // Check if product exists (only for product files with productId)
    if (type === 'product-file' && productId) {
      const product = await prisma.product.findUnique({
        where: { id: productId }
      })

      if (!product) {
        return NextResponse.json({
          success: false,
          message: 'Product not found'
        }, { status: 404 })
      }
    }

    // Convert file to buffer
    const buffer = Buffer.from(await file.arrayBuffer())

    // Generate unique file key
    const keyPrefix = type === 'product-image' ? 'images' : 'product'
    const fileKey = generateFileKey(keyPrefix, file.name)

    // Upload to R2
    const uploadResult = await uploadFile(
      buffer,
      fileKey,
      file.type,
      {
        originalName: file.name,
        productId: productId || null,
        uploadedBy: 'admin',
        uploadedAt: new Date().toISOString(),
        type
      }
    )

    // Update product with file information (only for product files)
    if (type === 'product-file' && productId) {
      await prisma.product.update({
        where: { id: productId },
        data: {
          fileKey: uploadResult.key,
          fileName: file.name,
          fileSize: BigInt(file.size)
        }
      })
    }

    return NextResponse.json({
      success: true,
      message: 'File uploaded successfully',
      data: {
        fileKey: uploadResult.key,
        fileName: file.name,
        fileSize: file.size,
        url: uploadResult.url
      }
    })

  } catch (error: any) {
    console.error('Error uploading file:', error)
    return NextResponse.json({
      success: false,
      message: error.message || 'Failed to upload file'
    }, { status: 500 })
  }
}
